class EnvironmentSettings:
    def __init__(self):
        self.workspace_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT'    # Base directory for saving network checkpoints.
        self.tensorboard_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/tensorboard'    # Directory for tensorboard files.
        self.pretrained_networks = '/home/<USER>/STU/workspaces/ruihui/ViPT/pretrained_networks'
        self.got10k_val_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/got10k/val'
        self.lasot_lmdb_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/lasot_lmdb'
        self.got10k_lmdb_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/got10k_lmdb'
        self.trackingnet_lmdb_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/trackingnet_lmdb'
        self.coco_lmdb_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/coco_lmdb'
        self.coco_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/coco'
        self.lasot_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/lasot'
        self.got10k_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/got10k/train'
        self.trackingnet_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/trackingnet'
        self.depthtrack_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/depthtrack/train'
        self.lasher_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/lasher/trainingset'
        self.visevent_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/VisEvent/train'
        self.coesot_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/COESOT/train'
        self.visevent_val = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/VisEvent/test'
        self.coesot_val = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/COESOT/test'
        self.felt_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/FELT/train'
        self.felt_val = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/FELT/test'
        self.fe108_dir = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/FE108/train'
        self.fe108_val = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/FE108/test'
