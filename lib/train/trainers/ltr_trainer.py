import os
import datetime
from collections import OrderedDict
from lib.train.trainers import BaseTrainer
from lib.train.admin import AverageMeter, StatValue
from lib.train.admin import TensorboardWriter
import torch
import time
import numpy as np
from torch.utils.data.distributed import DistributedSampler
from torch.cuda.amp import autocast
from torch.cuda.amp import GradScaler
from lib.utils.misc import get_world_size


class LTRTrainer(BaseTrainer):
    def _print_param_grad_update_stats(self):
        """打印本轮训练所有参数的均值、梯度均值、更新值均值（grad + weight_decay * param）和最终更新量均值（lr*update），便于整体观察"""
        # 收集所有参数、梯度和更新值到GPU tensor列表
        param_tensors, grad_tensors, update_tensors, final_update_tensors = [], [], [], []
        weight_decay = self.optimizer.param_groups[0].get('weight_decay', 0.0)
        lr = self.optimizer.param_groups[0].get('lr', 1.0)

        # 统计参数数量
        total_params = 0
        trainable_params = 0
        voxel_params = 0

        for name, param in self.actor.net.named_parameters():
            total_params += 1
            if param.requires_grad:
                trainable_params += 1
                if "voxel" in name:
                    voxel_params += 1

            if param.grad is not None:
                # 保持在GPU上计算，避免CPU-GPU数据传输
                param_data = param.data.detach()
                grad = param.grad.detach()
                update = grad + weight_decay * param_data
                final_update = lr * update

                # 计算每个参数张量的均值并收集
                param_tensors.append(param_data.mean())
                grad_tensors.append(grad.mean())
                update_tensors.append(update.mean())
                final_update_tensors.append(final_update.mean())

        if param_tensors:
            # 在GPU上计算所有均值的均值，最后一次性转到CPU
            param_mean = torch.stack(param_tensors).mean().item()
            grad_mean = torch.stack(grad_tensors).mean().item()
            update_mean = torch.stack(update_tensors).mean().item()
            final_update_mean = torch.stack(final_update_tensors).mean().item()
            print(f"[Param/Grad/Update Mean] param: {param_mean:.6e}, grad: {grad_mean:.6e}, update: {update_mean:.6e}, lr*update: {final_update_mean:.6e}")
            print(f"[Param Count] total: {total_params}, trainable: {trainable_params}, voxel: {voxel_params}, with_grad: {len(param_tensors)}")
        else:
            print("[Param/Grad/Update Mean] No parameters with gradients to analyze.")
            print(f"[Param Count] total: {total_params}, trainable: {trainable_params}, voxel: {voxel_params}, with_grad: 0")


    def _print_grad_stats_by_layer(self):
        """分别统计每一层的梯度信息"""
        for name, p in self.actor.net.named_parameters():
            if p.grad is not None:
                grads = p.grad.detach().view(-1)
                grad_norm = grads.norm(2).item()
                grad_abs_mean = grads.abs().mean().item()
                grad_abs_max = grads.abs().max().item()
                grad_abs_min = grads.abs().min().item()
                grad_mean = grads.mean().item()
                grad_std = grads.std().item()
                grad_nan = torch.isnan(grads).sum().item()
                grad_zero = (grads == 0).sum().item()
                grad_total = grads.numel()
                print(f"[Grad Stats of {name}] L2 norm: {grad_norm:.6e} | abs_mean: {grad_abs_mean:.3e} | abs_max: {grad_abs_max:.3e} | abs_min: {grad_abs_min:.3e} | mean: {grad_mean:.3e} | std: {grad_std:.3e} | nan: {int(grad_nan)} | zero: {int(grad_zero)}/{grad_total}")

    def __init__(self, actor, loaders, optimizer, settings, lr_scheduler=None, use_amp=False):
        """
        args:
            actor - The actor for training the network
            loaders - list of dataset loaders, e.g. [train_loader, val_loader]. In each epoch, the trainer runs one
                        epoch for each loader.
            optimizer - The optimizer used for training, e.g. Adam
            settings - Training settings
            lr_scheduler - Learning rate scheduler
        """
        super().__init__(actor, loaders, optimizer, settings, lr_scheduler)

        self._set_default_settings()

        # Initialize statistics variables
        self.stats = OrderedDict({loader.name: None for loader in self.loaders})

        # Initialize tensorboard
        if settings.local_rank in [-1, 0]:
            tensorboard_writer_dir = os.path.join(self.settings.env.tensorboard_dir, self.settings.project_path)
            if not os.path.exists(tensorboard_writer_dir):
                os.makedirs(tensorboard_writer_dir)
            self.tensorboard_writer = TensorboardWriter(tensorboard_writer_dir, [l.name for l in loaders])

            if settings.use_wandb:
                world_size = get_world_size()
                cur_train_samples = self.loaders[0].dataset.samples_per_epoch * max(0, self.epoch - 1)
                interval = (world_size * settings.batchsize)  # * interval

        self.move_data_to_gpu = getattr(settings, 'move_data_to_gpu', True)
        self.settings = settings
        self.use_amp = use_amp
        if use_amp:
            self.scaler = GradScaler()

    def _set_default_settings(self):
        # Dict of all default values
        default = {'print_interval': 10,
                   'print_stats': None,
                   'description': ''}

        for param, default_value in default.items():
            if getattr(self.settings, param, None) is None:
                setattr(self.settings, param, default_value)

    def cycle_dataset(self, loader):
        """Do a cycle of training or validation."""
        # torch.autograd.set_detect_anomaly(True)  # 开启异常检测
        self.actor.train(loader.training)
        torch.set_grad_enabled(loader.training)


        '''add fix rgb pretrained net bn, only used in box_head'''
        if self.settings.fix_bn:
            self.actor.fix_bns()

        self._init_timing()

        idx = 0

        for i, data in enumerate(loader, 1):
            self.data_read_done_time = time.time()
            # get inputs
            if self.move_data_to_gpu:
                data = data.to(self.device)

            self.data_to_gpu_time = time.time()

            data['epoch'] = self.epoch
            data['settings'] = self.settings
            # forward pass
            if not self.use_amp:
                loss, stats = self.actor(data)
            else:
                with autocast():
                    loss, stats = self.actor(data)

            # backward pass and update weights
            if loader.training:
                self.optimizer.zero_grad()
                if not self.use_amp:
                    loss.backward()
                    if self.settings.grad_clip_norm > 0:
                        torch.nn.utils.clip_grad_norm_(self.actor.net.parameters(), self.settings.grad_clip_norm)
                    if idx == 50:
                        idx = 0
                        self._print_param_grad_update_stats()
                        # self._print_grad_stats_by_layer()  # 打印梯度统计信息
                    idx += 1
                    self.optimizer.step()
                else:
                    self.scaler.scale(loss).backward()
                    self.scaler.step(self.optimizer)
                    self.scaler.update()
                    self._print_grad_stats_by_layer()  # 打印梯度统计信息

            # update statistics
            batch_size = data['template_images'].shape[loader.stack_dim]
            self._update_stats(stats, batch_size, loader)

            # print statistics
            self._print_stats(i, loader, batch_size)

        # calculate ETA after every epoch
        epoch_time = self.prev_time - self.start_time
        # self._print_grad_stats_by_layer()
        print("Epoch Time: " + str(datetime.timedelta(seconds=epoch_time)))
        print("Avg Data Time: %.5f" % (self.avg_date_time / self.num_frames * batch_size))
        print("Avg GPU Trans Time: %.5f" % (self.avg_gpu_trans_time / self.num_frames * batch_size))
        print("Avg Forward Time: %.5f" % (self.avg_forward_time / self.num_frames * batch_size))

    def train_epoch(self):
        """Do one epoch for each loader."""
        for loader in self.loaders:
            if self.epoch % loader.epoch_interval == 0:
                # 2021.1.10 Set epoch
                if isinstance(loader.sampler, DistributedSampler):
                    loader.sampler.set_epoch(self.epoch)
                self.cycle_dataset(loader)

        self._stats_new_epoch()
        if self.settings.local_rank in [-1, 0]:
            self._write_tensorboard()

    def _init_timing(self):
        self.num_frames = 0
        self.start_time = time.time()
        self.prev_time = self.start_time
        self.avg_date_time = 0
        self.avg_gpu_trans_time = 0
        self.avg_forward_time = 0

    def _update_stats(self, new_stats: OrderedDict, batch_size, loader):
        # Initialize stats if not initialized yet
        if loader.name not in self.stats.keys() or self.stats[loader.name] is None:
            self.stats[loader.name] = OrderedDict({name: AverageMeter() for name in new_stats.keys()})

        # add lr state
        if loader.training:
            lr_list = self.lr_scheduler.get_last_lr()
            for i, lr in enumerate(lr_list):
                var_name = 'LearningRate/group{}'.format(i)
                if var_name not in self.stats[loader.name].keys():
                    self.stats[loader.name][var_name] = StatValue()
                self.stats[loader.name][var_name].update(lr)

        for name, val in new_stats.items():
            if name not in self.stats[loader.name].keys():
                self.stats[loader.name][name] = AverageMeter()
            self.stats[loader.name][name].update(val, batch_size)

    def _print_stats(self, i, loader, batch_size):
        self.num_frames += batch_size
        current_time = time.time()
        batch_fps = batch_size / (current_time - self.prev_time)
        average_fps = self.num_frames / (current_time - self.start_time)
        prev_frame_time_backup = self.prev_time
        self.prev_time = current_time

        self.avg_date_time += (self.data_read_done_time - prev_frame_time_backup)
        self.avg_gpu_trans_time += (self.data_to_gpu_time - self.data_read_done_time)
        self.avg_forward_time += current_time - self.data_to_gpu_time

        if i % self.settings.print_interval == 0 or i == loader.__len__():
            print_str = '[%s: %d, %d / %d] ' % (loader.name, self.epoch, i, loader.__len__())
            print_str += 'FPS: %.1f (%.1f)  ,  ' % (average_fps, batch_fps)

            # 2021.12.14 add data time print
            print_str += 'DataTime: %.3f (%.3f)  ,  ' % (self.avg_date_time / self.num_frames * batch_size, self.avg_gpu_trans_time / self.num_frames * batch_size)
            print_str += 'ForwardTime: %.3f  ,  ' % (self.avg_forward_time / self.num_frames * batch_size)
            print_str += 'TotalTime: %.3f  ,  ' % ((current_time - self.start_time) / self.num_frames * batch_size)
            # print_str += 'DataTime: %.3f (%.3f)  ,  ' % (self.data_read_done_time - prev_frame_time_backup, self.data_to_gpu_time - self.data_read_done_time)
            # print_str += 'ForwardTime: %.3f  ,  ' % (current_time - self.data_to_gpu_time)
            # print_str += 'TotalTime: %.3f  ,  ' % (current_time - prev_frame_time_backup)

            for name, val in self.stats[loader.name].items():
                if (self.settings.print_stats is None or name in self.settings.print_stats):
                    if hasattr(val, 'avg'):
                        print_str += '%s: %.5f  ,  ' % (name, val.avg)
                    # else:
                    #     print_str += '%s: %r  ,  ' % (name, val)

            print(print_str[:-5])
            log_str = print_str[:-5] + '\n'
            with open(self.settings.log_file, 'a') as f:
                f.write(log_str)
            # self._print_grad_stats_by_layer()

    def _stats_new_epoch(self):
        # Record learning rate
        for loader in self.loaders:
            if loader.training:
                try:
                    lr_list = self.lr_scheduler.get_last_lr()
                except:
                    lr_list = self.lr_scheduler._get_lr(self.epoch)
                for i, lr in enumerate(lr_list):
                    var_name = 'LearningRate/group{}'.format(i)
                    if var_name not in self.stats[loader.name].keys():
                        self.stats[loader.name][var_name] = StatValue()
                    self.stats[loader.name][var_name].update(lr)

        for loader_stats in self.stats.values():
            if loader_stats is None:
                continue
            for stat_value in loader_stats.values():
                if hasattr(stat_value, 'new_epoch'):
                    stat_value.new_epoch()

    def _write_tensorboard(self):
        if self.epoch == 1:
            self.tensorboard_writer.write_info(self.settings.script_name, self.settings.description)

        self.tensorboard_writer.write_epoch(self.stats, self.epoch)
