import os
import datetime
from collections import OrderedDict
from lib.train.trainers import BaseTrainer
from lib.train.admin import AverageMeter, StatValue
from lib.train.admin import TensorboardWriter
import torch
import time
import numpy as np
from torch.utils.data.distributed import DistributedSampler
from torch.cuda.amp import autocast
from torch.cuda.amp import GradScaler
from lib.utils.misc import get_world_size


class LTRTrainer(BaseTrainer):
    def _print_param_grad_update_stats(self):
        """打印本轮训练所有参数的均值、梯度均值、更新值均值（grad + weight_decay * param）和最终更新量均值（lr*update），便于整体观察"""
        # 收集所有参数、梯度和更新值到GPU tensor列表
        param_tensors, grad_tensors, update_tensors, final_update_tensors = [], [], [], []
        weight_decay = self.optimizer.param_groups[0].get('weight_decay', 0.0)
        lr = self.optimizer.param_groups[0].get('lr', 1.0)

        # 统计参数数量
        total_params = 0
        trainable_params = 0
        voxel_params = 0

        for name, param in self.actor.net.named_parameters():
            total_params += 1
            if param.requires_grad:
                trainable_params += 1
                if "voxel" in name:
                    voxel_params += 1

            if param.grad is not None:
                # 保持在GPU上计算，避免CPU-GPU数据传输
                param_data = param.data.detach()
                grad = param.grad.detach()
                update = grad + weight_decay * param_data
                final_update = lr * update

                # 计算每个参数张量的均值并收集
                param_tensors.append(param_data.mean())
                grad_tensors.append(grad.mean())
                update_tensors.append(update.mean())
                final_update_tensors.append(final_update.mean())

        if param_tensors:
            # 在GPU上计算所有均值的均值，最后一次性转到CPU
            param_mean = torch.stack(param_tensors).mean().item()
            grad_mean = torch.stack(grad_tensors).mean().item()
            update_mean = torch.stack(update_tensors).mean().item()
            final_update_mean = torch.stack(final_update_tensors).mean().item()
            print(f"[Param/Grad/Update Mean] param: {param_mean:.6e}, grad: {grad_mean:.6e}, update: {update_mean:.6e}, lr*update: {final_update_mean:.6e}")
            print(f"[Param Count] total: {total_params}, trainable: {trainable_params}, voxel: {voxel_params}, with_grad: {len(param_tensors)}")
        else:
            print("[Param/Grad/Update Mean] No parameters with gradients to analyze.")
            print(f"[Param Count] total: {total_params}, trainable: {trainable_params}, voxel: {voxel_params}, with_grad: 0")


    def _print_grad_stats_by_layer(self):
        """分别统计每一层的梯度信息"""
        for name, p in self.actor.net.named_parameters():
            if p.grad is not None:
                grads = p.grad.detach().view(-1)
                grad_norm = grads.norm(2).item()
                grad_abs_mean = grads.abs().mean().item()
                grad_abs_max = grads.abs().max().item()
                grad_abs_min = grads.abs().min().item()
                grad_mean = grads.mean().item()
                grad_std = grads.std().item()
                grad_nan = torch.isnan(grads).sum().item()
                grad_zero = (grads == 0).sum().item()
                grad_total = grads.numel()
                print(f"[Grad Stats of {name}] L2 norm: {grad_norm:.6e} | abs_mean: {grad_abs_mean:.3e} | abs_max: {grad_abs_max:.3e} | abs_min: {grad_abs_min:.3e} | mean: {grad_mean:.3e} | std: {grad_std:.3e} | nan: {int(grad_nan)} | zero: {int(grad_zero)}/{grad_total}")

    def _collect_batch_param_stats(self, loader):
        """收集单个batch的参数统计信息，在optimizer.step()前调用"""
        if not self.param_stats_enabled or not loader.training:
            return

        weight_decay = self.optimizer.param_groups[0].get('weight_decay', 0.0)
        lr = self.optimizer.param_groups[0].get('lr', 1.0)

        batch_stats = {}

        # 整体统计
        param_tensors, grad_norms, update_norms = [], [], []

        # 层级聚合统计 - 用于收集同一模块下的参数
        module_stats = {}

        for name, param in self.actor.net.named_parameters():
            if param.grad is not None:
                # 保持在GPU上计算，避免CPU-GPU数据传输
                param_data = param.data.detach()
                grad = param.grad.detach()
                update = lr * (grad + weight_decay * param_data)

                # 计算统计值
                param_mean = param_data.mean().item()
                grad_norm = grad.norm(2).item()
                update_norm = update.norm(2).item()

                # 收集整体统计
                param_tensors.append(param_data.mean())
                grad_norms.append(grad_norm)
                update_norms.append(update_norm)

                # 层级聚合：将 weight/bias 聚合到上一级模块
                # 例如：backbone.layer1.conv.weight -> backbone.layer1.conv
                parts = name.split('.')
                if len(parts) > 1 and parts[-1] in ['weight', 'bias']:
                    # 移除最后一级（weight/bias），聚合到模块级别
                    module_name = '.'.join(parts[:-1])
                else:
                    # 如果不是标准的weight/bias结构，保持原名
                    module_name = name

                # 转换为tensorboard路径格式
                module_path = module_name.replace('.', '/')

                # 聚合到模块级别
                if module_path not in module_stats:
                    module_stats[module_path] = {
                        'param_means': [],
                        'grad_norms': [],
                        'update_norms': []
                    }

                module_stats[module_path]['param_means'].append(param_mean)
                module_stats[module_path]['grad_norms'].append(grad_norm)
                module_stats[module_path]['update_norms'].append(update_norm)

        # 计算整体统计
        if param_tensors:
            batch_stats['Params/Overall/param_mean'] = torch.stack(param_tensors).mean().item()
            batch_stats['Params/Overall/grad_norm'] = sum(grad_norms) / len(grad_norms)
            batch_stats['Params/Overall/update_norm'] = sum(update_norms) / len(update_norms)
            batch_stats['Params/Overall/layers_with_grad'] = len(param_tensors)

        # 计算模块级别的聚合统计
        for module_path, stats in module_stats.items():
            batch_stats[f'Params/{module_path}/param_mean'] = sum(stats['param_means']) / len(stats['param_means'])
            batch_stats[f'Params/{module_path}/grad_norm'] = sum(stats['grad_norms']) / len(stats['grad_norms'])
            batch_stats[f'Params/{module_path}/update_norm'] = sum(stats['update_norms']) / len(stats['update_norms'])

        # 将batch统计添加到epoch累积器中
        self.epoch_param_stats[loader.name].append(batch_stats)

    def _process_epoch_param_stats(self):
        """处理epoch级别的参数统计，计算平均值并添加到stats中"""
        for loader_name, batch_stats_list in self.epoch_param_stats.items():
            if not batch_stats_list:  # 如果没有收集到统计信息，跳过
                continue

            # 初始化loader的stats（如果还没有）
            if loader_name not in self.stats.keys() or self.stats[loader_name] is None:
                self.stats[loader_name] = OrderedDict()

            # 收集所有batch的统计信息并计算平均值
            param_stat_names = set()
            for batch_stats in batch_stats_list:
                param_stat_names.update(batch_stats.keys())

            # 为每个参数统计项计算epoch平均值
            for stat_name in param_stat_names:
                values = []
                for batch_stats in batch_stats_list:
                    if stat_name in batch_stats:
                        values.append(batch_stats[stat_name])

                if values:
                    # 计算平均值
                    avg_value = sum(values) / len(values)

                    # 添加到stats中
                    if stat_name not in self.stats[loader_name]:
                        if stat_name.endswith('layers_with_grad'):
                            self.stats[loader_name][stat_name] = StatValue()
                        else:
                            self.stats[loader_name][stat_name] = StatValue()

                    self.stats[loader_name][stat_name].update(avg_value)

            # 清空当前epoch的累积器，为下一个epoch做准备
            self.epoch_param_stats[loader_name] = []

    def __init__(self, actor, loaders, optimizer, settings, lr_scheduler=None, use_amp=False):
        """
        args:
            actor - The actor for training the network
            loaders - list of dataset loaders, e.g. [train_loader, val_loader]. In each epoch, the trainer runs one
                        epoch for each loader.
            optimizer - The optimizer used for training, e.g. Adam
            settings - Training settings
            lr_scheduler - Learning rate scheduler
        """
        super().__init__(actor, loaders, optimizer, settings, lr_scheduler)

        self._set_default_settings()

        # Initialize statistics variables
        self.stats = OrderedDict({loader.name: None for loader in self.loaders})

        # Initialize parameter statistics collection
        self.param_stats_enabled = getattr(settings, 'collect_param_stats', True)

        # Initialize epoch-level parameter statistics accumulators
        self.epoch_param_stats = OrderedDict({loader.name: [] for loader in self.loaders})

        # Initialize tensorboard
        if settings.local_rank in [-1, 0]:
            tensorboard_writer_dir = os.path.join(self.settings.env.tensorboard_dir, self.settings.project_path)
            if not os.path.exists(tensorboard_writer_dir):
                os.makedirs(tensorboard_writer_dir)
            self.tensorboard_writer = TensorboardWriter(tensorboard_writer_dir, [l.name for l in loaders])

            if settings.use_wandb:
                world_size = get_world_size()
                cur_train_samples = self.loaders[0].dataset.samples_per_epoch * max(0, self.epoch - 1)
                interval = (world_size * settings.batchsize)  # * interval

        self.move_data_to_gpu = getattr(settings, 'move_data_to_gpu', True)
        self.settings = settings
        self.use_amp = use_amp
        if use_amp:
            self.scaler = GradScaler()

    def _set_default_settings(self):
        # Dict of all default values
        default = {'print_interval': 10,
                   'print_stats': None,
                   'description': ''}

        for param, default_value in default.items():
            if getattr(self.settings, param, None) is None:
                setattr(self.settings, param, default_value)

    def cycle_dataset(self, loader):
        """Do a cycle of training or validation."""
        # torch.autograd.set_detect_anomaly(True)  # 开启异常检测
        self.actor.train(loader.training)
        torch.set_grad_enabled(loader.training)


        '''add fix rgb pretrained net bn, only used in box_head'''
        if self.settings.fix_bn:
            self.actor.fix_bns()

        self._init_timing()

        idx = 0

        for i, data in enumerate(loader, 1):
            self.data_read_done_time = time.time()
            # get inputs
            if self.move_data_to_gpu:
                data = data.to(self.device)

            self.data_to_gpu_time = time.time()

            data['epoch'] = self.epoch
            data['settings'] = self.settings
            # forward pass
            if not self.use_amp:
                loss, stats = self.actor(data)
            else:
                with autocast():
                    loss, stats = self.actor(data)

            # backward pass and update weights
            if loader.training:
                self.optimizer.zero_grad()
                if not self.use_amp:
                    loss.backward()
                    if self.settings.grad_clip_norm > 0:
                        torch.nn.utils.clip_grad_norm_(self.actor.net.parameters(), self.settings.grad_clip_norm)

                    # 收集参数统计信息（在step前）
                    self._collect_batch_param_stats(loader)

                    if idx == 50:
                        idx = 0
                        self._print_param_grad_update_stats()
                        # self._print_grad_stats_by_layer()  # 打印梯度统计信息
                    idx += 1
                    self.optimizer.step()
                else:
                    self.scaler.scale(loss).backward()

                    # 收集参数统计信息（在step前）
                    self._collect_batch_param_stats(loader)

                    self.scaler.step(self.optimizer)
                    self.scaler.update()
                    self._print_grad_stats_by_layer()  # 打印梯度统计信息

            # update statistics
            batch_size = data['template_images'].shape[loader.stack_dim]
            self._update_stats(stats, batch_size, loader)

            # print statistics
            self._print_stats(i, loader, batch_size)

        # calculate ETA after every epoch
        epoch_time = self.prev_time - self.start_time
        # self._print_grad_stats_by_layer()
        print("Epoch Time: " + str(datetime.timedelta(seconds=epoch_time)))
        print("Avg Data Time: %.5f" % (self.avg_date_time / self.num_frames * batch_size))
        print("Avg GPU Trans Time: %.5f" % (self.avg_gpu_trans_time / self.num_frames * batch_size))
        print("Avg Forward Time: %.5f" % (self.avg_forward_time / self.num_frames * batch_size))

    def train_epoch(self):
        """Do one epoch for each loader."""
        for loader in self.loaders:
            if self.epoch % loader.epoch_interval == 0:
                # 2021.1.10 Set epoch
                if isinstance(loader.sampler, DistributedSampler):
                    loader.sampler.set_epoch(self.epoch)
                self.cycle_dataset(loader)

        self._stats_new_epoch()
        if self.settings.local_rank in [-1, 0]:
            self._write_tensorboard()

    def _init_timing(self):
        self.num_frames = 0
        self.start_time = time.time()
        self.prev_time = self.start_time
        self.avg_date_time = 0
        self.avg_gpu_trans_time = 0
        self.avg_forward_time = 0

    def _update_stats(self, new_stats: OrderedDict, batch_size, loader):
        # Initialize stats if not initialized yet
        if loader.name not in self.stats.keys() or self.stats[loader.name] is None:
            self.stats[loader.name] = OrderedDict({name: AverageMeter() for name in new_stats.keys()})

        # add lr state
        if loader.training:
            lr_list = self.lr_scheduler.get_last_lr()
            for i, lr in enumerate(lr_list):
                var_name = 'LearningRate/group{}'.format(i)
                if var_name not in self.stats[loader.name].keys():
                    self.stats[loader.name][var_name] = StatValue()
                self.stats[loader.name][var_name].update(lr)

        for name, val in new_stats.items():
            if name not in self.stats[loader.name].keys():
                self.stats[loader.name][name] = AverageMeter()
            self.stats[loader.name][name].update(val, batch_size)

    def _print_stats(self, i, loader, batch_size):
        self.num_frames += batch_size
        current_time = time.time()
        batch_fps = batch_size / (current_time - self.prev_time)
        average_fps = self.num_frames / (current_time - self.start_time)
        prev_frame_time_backup = self.prev_time
        self.prev_time = current_time

        self.avg_date_time += (self.data_read_done_time - prev_frame_time_backup)
        self.avg_gpu_trans_time += (self.data_to_gpu_time - self.data_read_done_time)
        self.avg_forward_time += current_time - self.data_to_gpu_time

        if i % self.settings.print_interval == 0 or i == loader.__len__():
            print_str = '[%s: %d, %d / %d] ' % (loader.name, self.epoch, i, loader.__len__())
            print_str += 'FPS: %.1f (%.1f)  ,  ' % (average_fps, batch_fps)

            # 2021.12.14 add data time print
            print_str += 'DataTime: %.3f (%.3f)  ,  ' % (self.avg_date_time / self.num_frames * batch_size, self.avg_gpu_trans_time / self.num_frames * batch_size)
            print_str += 'ForwardTime: %.3f  ,  ' % (self.avg_forward_time / self.num_frames * batch_size)
            print_str += 'TotalTime: %.3f  ,  ' % ((current_time - self.start_time) / self.num_frames * batch_size)
            # print_str += 'DataTime: %.3f (%.3f)  ,  ' % (self.data_read_done_time - prev_frame_time_backup, self.data_to_gpu_time - self.data_read_done_time)
            # print_str += 'ForwardTime: %.3f  ,  ' % (current_time - self.data_to_gpu_time)
            # print_str += 'TotalTime: %.3f  ,  ' % (current_time - prev_frame_time_backup)

            for name, val in self.stats[loader.name].items():
                if (self.settings.print_stats is None or name in self.settings.print_stats):
                    if hasattr(val, 'avg'):
                        print_str += '%s: %.5f  ,  ' % (name, val.avg)
                    # else:
                    #     print_str += '%s: %r  ,  ' % (name, val)

            print(print_str[:-5])
            log_str = print_str[:-5] + '\n'
            with open(self.settings.log_file, 'a') as f:
                f.write(log_str)
            # self._print_grad_stats_by_layer()

    def _stats_new_epoch(self):
        # Record learning rate
        for loader in self.loaders:
            if loader.training:
                try:
                    lr_list = self.lr_scheduler.get_last_lr()
                except:
                    lr_list = self.lr_scheduler._get_lr(self.epoch)
                for i, lr in enumerate(lr_list):
                    var_name = 'LearningRate/group{}'.format(i)
                    if var_name not in self.stats[loader.name].keys():
                        self.stats[loader.name][var_name] = StatValue()
                    self.stats[loader.name][var_name].update(lr)

        # 处理epoch级别的参数统计
        self._process_epoch_param_stats()

        for loader_stats in self.stats.values():
            if loader_stats is None:
                continue
            for stat_value in loader_stats.values():
                if hasattr(stat_value, 'new_epoch'):
                    stat_value.new_epoch()

    def _write_tensorboard(self):
        if self.epoch == 1:
            self.tensorboard_writer.write_info(self.settings.script_name, self.settings.description)

        self.tensorboard_writer.write_epoch(self.stats, self.epoch)
