import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import MessagePassing, fps, knn_graph
from torch_geometric.utils import remove_self_loops
from torch_scatter import scatter_add

class VoxelFeatureExtractor(nn.Module):
    """
    一个对输入顺序不敏感的模块，用于从16个极性事件中提取特征。
    它直接将核心统计量作为输入，让一个简单的MLP去学习它们之间的非线性关系。
    """
    def __init__(self, feature_dim=32):
        super().__init__()
        
        # 输入一个MLP
        self.feature_encoder = nn.Sequential(
            nn.Linear(8, 16), # 输入8个核心统计量
            nn.ReLU(inplace=True),
            nn.Linear(16, feature_dim) # 直接输出最终维度
        )

    def forward(self, polarities, coords):
        """
        输入: polarities [B, N, 16]
        """
        # --- 计算核心统计特征 ---
        pos_counts = (polarities > 0).float().sum(dim=-1, keepdim=True)
        neg_counts = (polarities < 0).float().sum(dim=-1, keepdim=True)
        total_counts = pos_counts + neg_counts
        
        # 计算平衡度（方向性）和正事件比例
        balance = pos_counts - neg_counts
        pos_ratio = pos_counts / (total_counts + 1e-6)
        
        # 组合5个核心统计量
        stats = torch.cat([
            total_counts, 
            pos_counts, 
            neg_counts, 
            balance, 
            pos_ratio
        ], dim=-1)
        combined_input = torch.cat([stats, coords], dim=-1) # [B, N, 8]

        final_features = self.feature_encoder(combined_input)  # [B, N, feature_dim]

        return final_features

class EnhancedDirectTokenGraphConv(MessagePassing):
    """
    增强版直接操作token的图卷积层，采用稳定的MLP注意力机制
    """
    def __init__(self, in_dim, out_dim, max_neighbors=16, dropout=0.1):
        super().__init__(aggr='add')
        self.in_dim = in_dim
        self.out_dim = out_dim
        self.max_neighbors = max_neighbors
        self.dropout = dropout
        
        # 特征转换网络
        self.feat_transform = nn.Sequential(
            nn.Linear(in_dim, out_dim),
            nn.LayerNorm(out_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout)
        )
        
        # 运动编码器：将时空差转换为运动特征
        self.motion_encoder = nn.Sequential(
            nn.Linear(3, out_dim // 4),  # (dt, dx, dy)
            nn.ReLU(inplace=True),
            nn.Linear(out_dim // 4, out_dim),
            nn.LayerNorm(out_dim)
        )
        
        # === 旧方案：稳定的MLP注意力机制 ===
        # 减少输入冗余：移除feature_diff，只保留核心信息
        attention_input_dim = out_dim * 4  # x_i + x_j + motion_features
        
        self.attention = nn.Sequential(
            nn.LayerNorm(attention_input_dim),
            nn.Linear(attention_input_dim, out_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout * 0.5),
            nn.Linear(out_dim, out_dim // 2),
            nn.ReLU(inplace=True),
            nn.Linear(out_dim // 2, 1),
            nn.Sigmoid()
        )
        # === MLP注意力机制结束 ===
        
        # 残差连接
        self.residual = nn.Linear(in_dim, out_dim) if in_dim != out_dim else nn.Identity()
        
    def forward(self, x, pos, batch):
        """
        Args:
            x: [N, in_dim] 节点特征
            pos: [N, 3] 节点位置 (t, x, y) - 已标准化
            batch: [N] 批次索引
        """
        # 自适应确定邻域大小
        unique_batch, counts = torch.unique(batch, return_counts=True)
        avg_points_per_batch = counts.float().mean()
        k = min(self.max_neighbors, max(4, int(avg_points_per_batch * 0.2)))
        
        # 构建KNN图
        edge_index = knn_graph(pos, k=k, batch=batch, loop=False)
        edge_index, _ = remove_self_loops(edge_index)
        
        # 转换特征
        x_transformed = self.feat_transform(x)
        
        # 执行消息传递
        out = self.propagate(edge_index, x=x_transformed, pos=pos)
        
        # 残差连接
        out = out + self.residual(x)
        
        return out

    def message(self, x_i, x_j, pos_i, pos_j):
        """
        使用稳定的MLP注意力机制进行消息计算
        """
        # 1. 计算运动向量和运动特征
        motion_vec = pos_j - pos_i  # [num_edges, 3]
        motion_features = self.motion_encoder(motion_vec)  # [num_edges, out_dim]
        
        feature_diff = x_i - x_j  # [num_edges, out_dim]
        # 2. 组合输入信息（移除冗余的feature_diff）
        attn_input = torch.cat([
            x_i,              # 中心节点特征
            x_j,              # 邻居节点特征  
            feature_diff,     # 特征差
            motion_features   # 编码后的运动关系
        ], dim=1)  # [num_edges, 3*out_dim]
        
        # 3. 计算注意力权重
        attention_weights = self.attention(attn_input)  # [num_edges, 1]
        
        # 4. 构建最终消息
        message = attention_weights * (x_j + motion_features)
        
        return message

class AdaptiveFeatureBasedDownsample(nn.Module):
    """
    自适应特征驱动降采样模块
    - 前两层：点数不足时输出全部，不填充
    - 最后一层：必须输出固定数量，点数不足时均匀扩增
    """
    def __init__(self, target_points=256, is_final_layer=False):
        super().__init__()
        self.target_points = target_points
        self.is_final_layer = is_final_layer

    def forward(self, x, pos, batch):
        """
        Args:
            x: [N, D] 节点特征
            pos: [N, 3] 节点坐标
            batch: [N] 批次索引
        Returns:
            downsampled_x: 降采样后的特征
            downsampled_pos: 降采样后的坐标
            new_batch: 新的批次索引
        """
        B = 1  # 固定批次大小
        
        sampled_x_list = []
        sampled_pos_list = []
        sampled_batch_list = []

        for b in range(B):
            batch_mask = (batch == b)
            if not batch_mask.any():
                if self.is_final_layer:
                    # 最后一层必须输出固定数量
                    sampled_x_list.append(torch.zeros(self.target_points, x.shape[1], device=x.device))
                    sampled_pos_list.append(torch.zeros(self.target_points, 3, device=pos.device))
                    sampled_batch_list.append(torch.full((self.target_points,), b, dtype=torch.long, device=batch.device))
                else:
                    continue
                continue
            
            batch_x = x[batch_mask]      # [N_b, D]
            batch_pos = pos[batch_mask]  # [N_b, 3]
            N_b = batch_x.shape[0]

            # 计算重要性分数
            saliency_scores = torch.norm(batch_x, p=2, dim=1)  # [N_b]

            if N_b <= self.target_points:
                # 点数不足目标数量的处理
                if self.is_final_layer:
                    # 最后一层：均匀扩增到目标数量
                    sampled_x, sampled_pos = self._uniform_expand(
                        batch_x, batch_pos, saliency_scores, self.target_points
                    )
                else:
                    # 前面的层：输出全部点，不填充
                    sampled_x, sampled_pos = batch_x, batch_pos
            else:
                # 点数超过目标数量：进行Top-K采样
                _, topk_indices = torch.topk(saliency_scores, k=self.target_points)
                sampled_x = batch_x[topk_indices]
                sampled_pos = batch_pos[topk_indices]

            sampled_x_list.append(sampled_x)
            sampled_pos_list.append(sampled_pos)
            
            # 为当前批次创建正确的batch索引
            current_size = sampled_x.shape[0]
            sampled_batch_list.append(torch.full((current_size,), b, dtype=torch.long, device=batch.device))

        if len(sampled_x_list) == 0:
            # 处理所有样本都为空的情况
            if self.is_final_layer:
                return (torch.zeros(B * self.target_points, x.shape[1], device=x.device),
                        torch.zeros(B * self.target_points, 3, device=pos.device),
                        torch.arange(B, device=batch.device).repeat_interleave(self.target_points))
            else:
                return (torch.zeros(0, x.shape[1], device=x.device),
                        torch.zeros(0, 3, device=pos.device),
                        torch.zeros(0, dtype=torch.long, device=batch.device))

        # 合并所有batch
        final_x = torch.cat(sampled_x_list, dim=0)
        final_pos = torch.cat(sampled_pos_list, dim=0)
        final_batch = torch.cat(sampled_batch_list, dim=0)
        
        return final_x, final_pos, final_batch

    def _uniform_expand(self, x, pos, saliency_scores, target_size):
        """
        均匀扩增策略：通过零填充实现扩增
        """
        N = x.shape[0]
        
        if N == 0:
            return torch.zeros(target_size, x.shape[1], device=x.device), torch.zeros(target_size, 3, device=pos.device)
        
        # 按重要性排序
        _, sorted_indices = torch.sort(saliency_scores, descending=True)
        sorted_x = x[sorted_indices]
        sorted_pos = pos[sorted_indices]
        
        # 计算需要扩增的数量
        expand_count = target_size - N
        
        if expand_count <= 0:
            return sorted_x[:target_size], sorted_pos[:target_size]
        
        # 创建填充张量
        padding_x = torch.zeros(expand_count, x.shape[1], device=x.device)
        padding_pos = torch.zeros(expand_count, 3, device=pos.device)
        
        # 合并原始点和填充点
        final_x = torch.cat([sorted_x, padding_x], dim=0)
        final_pos = torch.cat([sorted_pos, padding_pos], dim=0)
        
        return final_x, final_pos

class DirectTokenMotionEncoder(nn.Module):
    """
    优化版运动编码器：消除样本隔离，使用批次并行处理
    """
    def __init__(self, input_dim=32, hidden_dims=[64, 128, 256], output_dim=768, 
                 target_tokens=256, dropout=0.1):
        super().__init__()
        self.input_dim = input_dim
        self.output_dim = output_dim
        self.target_tokens = target_tokens
        self.dropout = dropout
        
        # 分层降采样策略
        self.layer1_points = min(1024, target_tokens * 4)
        self.layer2_points = min(512, target_tokens * 2)
        self.final_points = target_tokens
        
        # 渐进式邻居数配置
        neighbor_configs = [24, 16, 8]  # 递减式邻居数
        
        # 多层图卷积
        dims = [input_dim] + hidden_dims
        self.graph_convs = nn.ModuleList()
        self.norms = nn.ModuleList()
        self.downsamplers = nn.ModuleList()
        
        target_points_list = [self.layer1_points, self.layer2_points, self.final_points]
        is_final_list = [False, False, True]
        
        for i in range(len(hidden_dims)):
            # 使用渐进式邻居数
            self.graph_convs.append(
                EnhancedDirectTokenGraphConv(
                    dims[i], dims[i+1], 
                    max_neighbors=neighbor_configs[i],
                    dropout=dropout
                )
            )
            self.norms.append(nn.LayerNorm(dims[i+1]))
            self.downsamplers.append(
                AdaptiveFeatureBasedDownsample(
                    target_points_list[i], 
                    is_final_layer=is_final_list[i]
                )
            )
        
        # 最终投影到目标维度
        self.final_projection = nn.Sequential(
            nn.Linear(dims[-1], output_dim),
            nn.LayerNorm(output_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout)
        )
        
        self.init_weights()
    
    def init_weights(self):
        """稳定的权重初始化"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight, gain=0.8)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)

    def forward(self, coords, features, valid_mask):
        """
        核心优化：批次并行处理，消除样本隔离开销
        
        Args:
            coords: [B, N, 3] 体素坐标（已标准化）
            features: [B, N, D] 体素特征
            valid_mask: [B, N] 有效点掩码
        
        Returns:
            token_features: [B, target_tokens, output_dim] 最终的token特征
            token_coords: [B, target_tokens, 3] 对应的坐标
        """
        B, N, D = features.shape
        
        # 批次并行的数据准备
        valid_coords_list = []
        valid_features_list = []
        batch_indices = []
        
        for b in range(B):
            mask = valid_mask[b]
            if mask.sum() > 0:
                valid_coords_list.append(coords[b][mask])
                valid_features_list.append(features[b][mask])
                batch_indices.append(torch.full((mask.sum(),), b, dtype=torch.long, device=coords.device))
        
        if len(valid_coords_list) == 0:
            # 处理全空情况
            zero_features = torch.zeros(B, self.target_tokens, self.output_dim, device=coords.device)
            zero_coords = torch.zeros(B, self.target_tokens, 3, device=coords.device)
            return zero_features, zero_coords
        
        # 批次并行处理：一次性处理所有样本的有效点
        all_coords = torch.cat(valid_coords_list, dim=0)
        all_features = torch.cat(valid_features_list, dim=0)
        all_batch = torch.cat(batch_indices, dim=0)
        
        # 批次并行的GNN处理
        x, pos, batch_idx = all_features, all_coords, all_batch
        
        for conv, norm, downsampler in zip(self.graph_convs, self.norms, self.downsamplers):
            # 图卷积（PyG自动处理批次隔离）
            x = conv(x, pos, batch_idx)
            x = norm(x)
            x = F.relu(x, inplace=True)
            x = F.dropout(x, p=self.dropout, training=self.training)
            
            # 降采样
            x, pos, batch_idx = downsampler(x, pos, batch_idx)
        
        # 最终投影
        final_features = self.final_projection(x)
        
        # 重新组织为批次格式
        token_features = final_features.view(B, self.target_tokens, self.output_dim)
        token_coords = pos.view(B, self.target_tokens, 3)
        '''
        # --- 维度检查与安全填充 ---
        # 检查经过GNN处理后，是否还有剩余的有效点
        if final_features.shape[0] > 0:
            # 获取当前批次中实际处理过的样本索引
            unique_b_idx = torch.unique(batch_idx)
            
            # 创建一个完整尺寸的全零张量作为容器
            token_features = torch.zeros(B, self.target_tokens, self.output_dim, 
                                         device=final_features.device, dtype=final_features.dtype)
            token_coords = torch.zeros(B, self.target_tokens, 3, 
                                       device=pos.device, dtype=pos.dtype)
            
            # 将有效样本的特征和坐标填充到正确的位置
            # 1. 先将GNN输出的扁平化数据重塑为样本独立的形状
            valid_features = final_features.view(-1, self.target_tokens, self.output_dim)
            valid_coords = pos.view(-1, self.target_tokens, 3)
            
            # 2. 使用高级索引，将有效数据填充到全零容器中
            token_features[unique_b_idx] = valid_features
            token_coords[unique_b_idx] = valid_coords
        else:
            # 如果所有样本都没有有效点，则直接返回全零张量
            token_features = torch.zeros(B, self.target_tokens, self.output_dim, 
                                         device=features.device, dtype=features.dtype)
            token_coords = torch.zeros(B, self.target_tokens, 3, 
                                       device=coords.device, dtype=coords.dtype)
        ''' 
        return token_features, token_coords


class ImprovedCrossAttentionFusion(nn.Module):
    """
    改进的跨注意力融合模块，增加训练稳定性
    """
    def __init__(self, embed_dim=768, num_heads=8, dropout=0.1, initial_alpha=0.1):
        super().__init__()
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.initial_alpha = initial_alpha
        
        # 交叉注意力
        self.cross_attn = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            batch_first=True,
            dropout=dropout
        )
        
        # 特征融合网络
        self.fusion_net = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim)
        )
        
        # 自适应融合权重网络
        self.alpha_net = nn.Sequential(
            nn.Linear(embed_dim * 2, embed_dim // 4),
            nn.ReLU(inplace=True),
            nn.Linear(embed_dim // 4, 1)
        )
        
        # 可学习的全局偏置参数
        self.alpha_bias = nn.Parameter(torch.tensor(self.initial_alpha))
        
        self.init_weights()
    
    def init_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight, gain=0.8)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
        
        # 为alpha_net的最后一层设置特殊初始化
        final_layer = self.alpha_net[-1]
        nn.init.normal_(final_layer.weight, mean=0, std=0.01)
        nn.init.constant_(final_layer.bias, 0)

    def forward(self, search_features, motion_features):
        """
        Args:
            search_features: [B, L_s, embed_dim] RGB特征
            motion_features: [B, L_m, embed_dim] 运动特征
        """
        # 交叉注意力
        motion_context, attn_weights = self.cross_attn(
            query=search_features,
            key=motion_features,
            value=motion_features
        )
        
        # 特征融合
        fused_context = self.fusion_net(motion_context)
        
        # 带偏置的自适应融合权重
        alpha_input = torch.cat([search_features, fused_context], dim=-1)
        alpha_raw = self.alpha_net(alpha_input)
        alpha = torch.sigmoid(alpha_raw + self.alpha_bias)
        
        # 最终融合
        enhanced_features = search_features + alpha * fused_context
        
        # 统计信息
        motion_info = {
            'alpha': alpha.mean().item(),
            'alpha_bias': self.alpha_bias.item(),
            'alpha_std': alpha.std().item(),
            'attn_entropy': -(attn_weights * torch.log(attn_weights + 1e-8)).sum(dim=-1).mean().item()
        }
        
        return enhanced_features, motion_info


class VoxelMotionEnhancer(nn.Module):
    """
    基于图神经网络的体素运动增强器 - 优化版本
    """
    def __init__(self, embed_dim=768, num_heads=8, dropout=0.1):
        super().__init__()
        
        feature_dim = 32
        self.event_voxel_encoder = VoxelFeatureExtractor(feature_dim=feature_dim)
        
        # 使用优化版的token编码器
        self.token_encoder = DirectTokenMotionEncoder(
            input_dim=feature_dim,
            hidden_dims=[64, 128, 256],  # 恢复合理的维度配置
            output_dim=embed_dim,
            target_tokens=256,
            dropout=dropout
        )
        
        # 跨注意力融合
        self.cross_attn_fusion = ImprovedCrossAttentionFusion(
            embed_dim=embed_dim,
            num_heads=num_heads,
            dropout=dropout
        )

    def process_voxels(self, voxel_data):
        """处理体素数据，直接输出256个有意义的token"""
        voxel_data = voxel_data.squeeze(1).float()  # [B, 4096, 19]
        coords = voxel_data[:, :, :3]    # [B, 4096, 3] - 已标准化的坐标
        polarities = voxel_data[:, :, 3:] # [B, 4096, 16] - 极性特征

        # 创建有效点掩码
        valid_mask = coords.abs().sum(dim=-1) > 1e-6

        # 1. 提取体素特征
        voxel_features = self.event_voxel_encoder(polarities, coords)  # [B, 4096, feature_dim]

        # 2. 使用优化的批次并行方法生成256个token和对应坐标
        motion_tokens, token_coords = self.token_encoder(coords, voxel_features, valid_mask)
        
        return motion_tokens, token_coords

    def forward(self, search_features, search_voxels):
        """
        Args:
            search_features: [B, 256, embed_dim] RGB特征
            search_voxels: [B, 1, 1, 4096, 19] 体素数据
        """
        # 处理体素数据，得到256个运动token
        search_motion_tokens, token_coords = self.process_voxels(search_voxels)

        # 跨注意力融合
        enhanced_features, motion_info = self.cross_attn_fusion(
            search_features, search_motion_tokens
        )

        return enhanced_features, motion_info