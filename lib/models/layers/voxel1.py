import torch
import torch.nn as nn
import torch.nn.functional as F

class TemporalCompressor(nn.Module):
    """简洁的时间维度压缩模块，保留时空轨迹信息"""
    def __init__(self, channels):
        super().__init__()
        # 1D时间卷积捕获时序模式
        self.temp_conv = nn.Conv1d(channels, channels, kernel_size=3, padding=1)
        # 可学习的时间权重
        self.temp_attn = nn.Sequential(
            nn.Conv1d(channels, channels // 4, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv1d(channels // 4, 1, kernel_size=1),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        # x: [B, C, T, H, W]
        B, C, T, H, W = x.shape
        
        # 重塑为 [B*H*W, C, T] 进行时间建模
        reshaped = x.permute(0, 3, 4, 1, 2).reshape(B * H * W, C, T)
        
        # 时间卷积提取轨迹特征
        temp_features = F.relu(self.temp_conv(reshaped))
        
        # 计算每个时间步的重要性权重
        temp_weights = self.temp_attn(temp_features)  # [B*H*W, 1, T]
        
        # 加权聚合时间维度
        weighted_features = (temp_features * temp_weights).sum(dim=-1)  # [B*H*W, C]
        
        # 重塑回空间格式
        output = weighted_features.reshape(B, H, W, C).permute(0, 3, 1, 2)  # [B, C, H, W]
        
        return output


class VoxelEncoder(nn.Module):
    def __init__(self, embed_dim=768, grid_size=(8, 16, 16)):
        super().__init__()
        self.grid_size = grid_size  # t, h, w
        
        # 主干网络：分层特征提取
        self.conv3d = nn.Sequential(
            # Level 1: 细粒度特征
            nn.Conv3d(1, 32, kernel_size=(3, 3, 3), padding=(1, 1, 1)),
            nn.GroupNorm(8, 32), nn.ReLU(inplace=True),
            
            # Level 2: 中等粒度 + 空间下采样
            nn.Conv3d(32, 64, kernel_size=(3, 3, 3), padding=(1, 1, 1)),
            nn.GroupNorm(8, 64), nn.ReLU(inplace=True),
            nn.MaxPool3d(kernel_size=(1, 2, 2), stride=(1, 2, 2)),
            
            # Level 3: 全局趋势
            nn.Conv3d(64, 128, kernel_size=(3, 5, 5), padding=(1, 2, 2)),  # 扩大空间感受野
            nn.GroupNorm(16, 128), nn.ReLU(inplace=True),
            
            # Level 4: 长时间依赖
            nn.Conv3d(128, 64, kernel_size=(5, 3, 3), padding=(2, 1, 1)),  # 扩大时间感受野
            nn.GroupNorm(8, 64), nn.ReLU(inplace=True)
        )
        
        # 运动方向感知：并行提取
        self.motion_direction = nn.Sequential(
            nn.Conv3d(1, 16, kernel_size=(3, 1, 1), padding=(1, 0, 0)),  # 时间
            nn.Conv3d(16, 16, kernel_size=(1, 3, 1), padding=(0, 1, 0)),  # 垂直  
            nn.Conv3d(16, 16, kernel_size=(1, 1, 3), padding=(0, 0, 1)),  # 水平
            nn.ReLU(inplace=True)
        )
        
        # 时间差分：捕获运动变化
        self.temporal_diff = nn.Sequential(
            nn.Conv3d(1, 32, kernel_size=(2, 3, 3), padding=(0, 1, 1)),
            nn.ReLU(inplace=True)
        )
        
        # 特征融合
        self.feature_fusion = nn.Sequential(
            nn.Conv3d(64 + 16 + 32, 64, kernel_size=1),
            nn.GroupNorm(8, 64), nn.ReLU(inplace=True)
        )
        
        # 时间维度压缩器（保留轨迹信息）
        self.temporal_compressor = TemporalCompressor(64)
        
        # 空间对齐
        self.spatial_pool = nn.AdaptiveAvgPool2d((16, 16))
        
        # 投影
        self.projection = nn.Sequential(
            nn.Linear(64, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.Dropout(0.1)
        )

    def forward(self, voxel_grid):
        # 主干特征
        main_features = self.conv3d(voxel_grid)
        
        # 方向特征
        dir_features = self.motion_direction(voxel_grid)
        dir_features = F.adaptive_avg_pool3d(dir_features, main_features.shape[2:])
        
        # 差分特征
        if voxel_grid.shape[2] > 1:
            diff_features = self.temporal_diff(voxel_grid)
            diff_features = F.pad(diff_features, (0, 0, 0, 0, 1, 0))
            diff_features = F.adaptive_avg_pool3d(diff_features, main_features.shape[2:])
        else:
            diff_features = torch.zeros(main_features.shape[0], 32, *main_features.shape[2:], 
                                      device=main_features.device)
        
        # 融合所有特征
        combined = torch.cat([main_features, dir_features, diff_features], dim=1)
        fused = self.feature_fusion(combined)  # [B, 64, T, H, W]
        
        # 智能时间压缩：保留轨迹信息
        temporal_compressed = self.temporal_compressor(fused)  # [B, 64, H, W]
        
        # 空间对齐
        spatial_aligned = self.spatial_pool(temporal_compressed)  # [B, 64, 16, 16]
        
        # 转换为token格式并投影
        tokens = spatial_aligned.flatten(2).transpose(1, 2)  # [B, 256, 64]
        features = self.projection(tokens)  # [B, 256, embed_dim]
        
        return features

class SpatialAdaptiveFusion(nn.Module):
    """空间自适应的跨注意力融合模块"""
    def __init__(self, embed_dim=768, num_heads=8):
        super().__init__()
        self.cross_attn = nn.MultiheadAttention(embed_dim, num_heads, batch_first=True)
        
        # 运动评估网络
        self.motion_eval = nn.Sequential(
            nn.Linear(embed_dim, 32), nn.ReLU(),
            nn.Linear(32, 2), nn.Sigmoid()  # [强度, 一致性]
        )
        
        # 融合网络
        self.fusion = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.GELU(), nn.Dropout(0.1)
        )
        
        # 空间自适应权重预测器
        self.spatial_alpha_net = nn.Sequential(
            # 逐位置特征融合
            nn.Linear(embed_dim * 2, embed_dim // 2),  # search + motion特征
            nn.ReLU(inplace=True),
            nn.Linear(embed_dim // 2, embed_dim // 4),
            nn.ReLU(inplace=True),
            nn.Linear(embed_dim // 4, 1),
            nn.Sigmoid()
        )
        
        # 全局运动强度调节器
        self.global_intensity_net = nn.Sequential(
            nn.Linear(embed_dim + 2, 64), nn.ReLU(),  # 全局特征 + 运动指标
            nn.Linear(64, 1), nn.Sigmoid()
        )
        
        # 空间注意力：突出重要区域
        self.spatial_attention = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 4),
            nn.ReLU(inplace=True),
            nn.Linear(embed_dim // 4, 1),
            nn.Sigmoid()
        )
        
        self.base_alpha = 0.05
        self.alpha_scale = 0.4
    
    def forward(self, search_features, motion_features):
        # search_features: [B, N, D], motion_features: [B, N, D]
        B, N, D = search_features.shape
        
        # ============ 跨注意力获取运动上下文 ============
        motion_context, attn_weights = self.cross_attn(
            query=search_features, 
            key=motion_features, 
            value=motion_features
        )  # [B, N, D]
        
        # ============ 全局运动评估 ============
        motion_global = motion_features.mean(dim=1)  # [B, D]
        motion_metrics = self.motion_eval(motion_global)  # [B, 2]
        motion_int, motion_cons = motion_metrics.chunk(2, dim=1)
        
        # 全局运动强度因子
        global_intensity_input = torch.cat([motion_global, motion_metrics], dim=1)
        global_intensity = self.global_intensity_net(global_intensity_input)  # [B, 1]
        
        # ============ 空间自适应权重计算 ============
        # 方法1: 基于特征相似性的空间权重
        search_motion_concat = torch.cat([search_features, motion_context], dim=-1)  # [B, N, 2D]
        spatial_alpha = self.spatial_alpha_net(search_motion_concat)  # [B, N, 1]
        
        # 方法2: 空间注意力增强
        spatial_attention_weights = self.spatial_attention(motion_context)  # [B, N, 1]
        
        # ============ 多层次权重融合 ============
        # 结合全局强度、空间权重和空间注意力
        final_alpha = (
            self.base_alpha + 
            self.alpha_scale * 
            global_intensity.unsqueeze(1) *  # [B, 1, 1] - 全局调节
            spatial_alpha *                  # [B, N, 1] - 空间自适应
            spatial_attention_weights *      # [B, N, 1] - 注意力增强
            motion_int.unsqueeze(1)          # [B, 1, 1] - 运动强度调节
        )  # [B, N, 1]
        
        # ============ 空间自适应特征融合 ============
        enhanced_features = search_features + final_alpha * self.fusion(motion_context)
        
        return enhanced_features, {
            "attn_weights": attn_weights,
            "spatial_alpha": final_alpha,           # 空间分布的权重
            "global_intensity": global_intensity,   # 全局运动强度  
            "spatial_attention": spatial_attention_weights,  # 空间注意力
            "motion_intensity": motion_int,
            "motion_consistency": motion_cons
        }


class CrossAttentionFusion(nn.Module):
    def __init__(self, embed_dim=768, num_heads=8):
        super().__init__()
        self.cross_attn = nn.MultiheadAttention(embed_dim, num_heads, batch_first=True)
        
        # 简化的运动评估
        self.motion_eval = nn.Sequential(
            nn.Linear(embed_dim, 32), nn.ReLU(),
            nn.Linear(32, 2), nn.Sigmoid()  # [强度, 一致性]
        )
        
        # 融合网络
        self.fusion = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.LayerNorm(embed_dim),
            nn.GELU(), nn.Dropout(0.1)
        )
        
        # 动态权重预测
        self.alpha_net = nn.Sequential(
            nn.Linear(embed_dim + 2, 32), nn.ReLU(),
            nn.Linear(32, 1), nn.Sigmoid()
        )
        
        self.base_alpha = 0.05
        self.alpha_scale = 0.3  # 减小动态范围，更稳定
    
    def forward(self, search_features, motion_features):
        # 跨注意力
        motion_context, attn_weights = self.cross_attn(
            query=search_features, key=motion_features, value=motion_features
        )
        
        # 运动评估
        motion_global = motion_features.mean(dim=1)
        motion_metrics = self.motion_eval(motion_global)  # [B, 2]
        motion_int, motion_cons = motion_metrics.chunk(2, dim=1)
        
        # 动态权重
        alpha_input = torch.cat([motion_global, motion_metrics], dim=1)
        dynamic_alpha = self.alpha_net(alpha_input)
        alpha = (self.base_alpha + self.alpha_scale * dynamic_alpha * motion_int).unsqueeze(1)
        
        # 融合
        enhanced_features = search_features + alpha * self.fusion(motion_context)
        
        return enhanced_features, {
            "attn_weights": attn_weights, "alpha": alpha,
            "motion_intensity": motion_int, "motion_consistency": motion_cons
        }


class VoxelMotionEnhancer(nn.Module):
    def __init__(self, embed_dim=768, num_heads=8, grid_size=(8, 16, 16)):
        super().__init__()
        self.voxel_encoder = VoxelEncoder(embed_dim, grid_size)
        self.cross_fusion = SpatialAdaptiveFusion(embed_dim, num_heads)
    
    def forward(self, search_features, voxel_data):
        # 提取体素特征
        voxel_features = self.voxel_encoder(voxel_data)
        
        # 跨模态融合
        enhanced_features, motion_info = self.cross_fusion(search_features, voxel_features)
        
        return enhanced_features, motion_info