import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple

class VoxelAdapter(nn.Module):
    """
    (融合优化版) 双流深度一致性适配器
    结合高低流分离与深度互相关的优势
    """
    def __init__(self, embed_dim=768, motion_feat_dim=64, patch_size=16, search_size=256):
        super().__init__()
        self.embed_dim = embed_dim
        self.motion_feat_dim = motion_feat_dim
        self.patch_size = patch_size
        self.search_size = search_size
        
        # 计算patch网格尺寸
        self.patch_H = self.patch_W = search_size // patch_size
        
        # 1-3. 时序编码器、聚合、空间对齐 (保持不变)
        self.temporal_conv = nn.Sequential(
            nn.Conv3d(1, 16, kernel_size=(3, 3, 3), padding=(1, 1, 1)),
            nn.BatchNorm3d(16),
            nn.ReLU(inplace=True),
            nn.Conv3d(16, 8, kernel_size=(3, 3, 3), padding=(1, 1, 1)),
            nn.BatchNorm3d(8),
            nn.ReLU(inplace=True)
        )
        
        self.temporal_pool = nn.AdaptiveAvgPool3d((1, None, None))
        
        self.spatial_align = nn.Sequential(
            nn.Conv2d(8, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d((self.patch_H, self.patch_W)),
            nn.Conv2d(32, motion_feat_dim, kernel_size=1)
        )
        
        # 4. (新) 模板特征空间投影 - 用于生成互相关核
        self.template_spatial_proj = nn.Conv2d(embed_dim, motion_feat_dim, kernel_size=1)
        
        # 5. (新) 一致性图精炼网络 - 替换原有的逐像素MLP
        self.consistency_refine = nn.Sequential(
            nn.Conv2d(1, 16, kernel_size=3, padding=1),
            nn.BatchNorm2d(16),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 8, kernel_size=3, padding=1),
            nn.BatchNorm2d(8),
            nn.ReLU(inplace=True),
            nn.Conv2d(8, 1, kernel_size=1),
            nn.Sigmoid()
        )
        
        # 6. (优化) 对比融合网络 - 加入显式差值先验
        self.contrast_net = nn.Sequential(
            nn.Conv2d(3, 16, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 8, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(8, 1, kernel_size=1),
            nn.Sigmoid()
        )
        
        # 7. 最终注意力生成 (保持不变)
        self.attention_head = nn.Sequential(
            nn.Conv2d(1, 8, kernel_size=3, padding=1),
            nn.BatchNorm2d(8),
            nn.ReLU(inplace=True),
            nn.Conv2d(8, 1, kernel_size=1),
            nn.Sigmoid()
        )
        
        self._init_weights()

    def _init_weights(self):
        """权重初始化 (保持不变)"""
        for m in self.modules():
            if isinstance(m, (nn.Conv2d, nn.Conv3d, nn.Linear)):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, (nn.BatchNorm2d, nn.BatchNorm3d)):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def _compute_motion_separation(self, voxel_grid: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        计算高/低运动分离 (保持原有逻辑不变)
        """
        B, C, T, H, W = voxel_grid.shape
        
        # 计算时间活动强度
        temporal_activity = voxel_grid.sum(dim=[1, 3, 4])  # [B, T]
        
        # 动态阈值计算
        activity_mean = temporal_activity.mean(dim=1, keepdim=True)
        activity_std = temporal_activity.std(dim=1, keepdim=True)
        threshold = activity_mean + 0.2 * activity_std
        
        # 生成时间掩码
        high_motion_mask = (temporal_activity > threshold).float()  # [B, T]
        low_motion_mask = 1.0 - high_motion_mask
        
        # 确保每个流至少有一些时间步
        for b in range(B):
            if high_motion_mask[b].sum() == 0:
                high_motion_mask[b, temporal_activity[b].argmax()] = 1.0
            if low_motion_mask[b].sum() == 0:
                low_motion_mask[b, temporal_activity[b].argmin()] = 1.0
        
        # 应用掩码并聚合
        high_motion_grid = voxel_grid * high_motion_mask.view(B, 1, T, 1, 1).contiguous()
        low_motion_grid = voxel_grid * low_motion_mask.view(B, 1, T, 1, 1).contiguous()
        
        # 时序编码
        high_motion_feat = self.temporal_conv(high_motion_grid)
        low_motion_feat = self.temporal_conv(low_motion_grid)
        
        # 时序聚合
        high_motion_2d = self.temporal_pool(high_motion_feat).squeeze(2)
        low_motion_2d = self.temporal_pool(low_motion_feat).squeeze(2)
        
        # 空间对齐到patch网格
        high_motion_map = self.spatial_align(high_motion_2d)
        low_motion_map = self.spatial_align(low_motion_2d)
        
        return high_motion_map, low_motion_map
    
    def _compute_motion_template_consistency(self, motion_map: torch.Tensor, 
                                           template_features: torch.Tensor) -> torch.Tensor:
        """
        (核心优化) 使用深度互相关计算运动-模板一致性
        Args:
            motion_map: [B, motion_feat_dim, patch_H, patch_W]
            template_features: [B, N_template, embed_dim]
        Returns:
            consistency: [B, patch_H, patch_W]
        """
        B, C, H, W = motion_map.shape
        L_template = template_features.shape[1]
        
        # 1. 将模板token转换为特征图
        # 假设模板是方形排列
        H_template = W_template = int(L_template**0.5)
        template_map = template_features.transpose(1, 2).view(B, self.embed_dim, H_template, W_template).contiguous()

        # 2. 投影模板特征到运动特征空间
        template_kernel = self.template_spatial_proj(template_map) # [B, motion_feat_dim, H_t, W_t]
        
        # 3. 使用分组卷积实现深度互相关
        # 将每个样本的运动图和模板核分别处理
        motion_map_reshaped = motion_map.reshape(1, B * C, H, W)
        template_kernel_reshaped = template_kernel.reshape(B * C, 1, H_template, W_template)
        
        # 执行分组卷积
        consistency_map_flat = F.conv2d(
            motion_map_reshaped, 
            template_kernel_reshaped, 
            groups=B * C, 
            padding='same'
        )
        
        # 4. 恢复形状并聚合通道
        consistency_map = consistency_map_flat.view(B, C, H, W).contiguous()
        consistency_map = consistency_map.sum(dim=1, keepdim=True) # [B, 1, H, W]
        
        # 5. 使用CNN进行精炼
        refined_consistency = self.consistency_refine(consistency_map) # [B, 1, H, W]
        
        return refined_consistency.squeeze(1) # [B, H, W]

    def forward(self, voxel_grid: torch.Tensor, template_features: torch.Tensor) -> torch.Tensor:
        """
        (融合优化版) 前向传播
        保留高低流分离逻辑，优化一致性计算和对比融合
        """
        # 处理空输入
        if voxel_grid is None or voxel_grid.numel() == 0:
            B = template_features.shape[0]
            return torch.ones(B, 1, self.patch_H, self.patch_W, 
                             device=template_features.device) * 0.5
        
        B = voxel_grid.shape[0]
        
        # 1. 运动流分离 (保持原有逻辑)
        high_motion_map, low_motion_map = self._compute_motion_separation(voxel_grid)
        
        # 2. 计算运动-模板一致性 (使用新的深度互相关方法)
        high_consistency = self._compute_motion_template_consistency(high_motion_map, template_features)
        low_consistency = self._compute_motion_template_consistency(low_motion_map, template_features)
        
        # 3. (优化) 对比融合 - 加入显式差值先验
        # 3a. 计算显式差值
        explicit_diff = high_consistency - low_consistency # [B, H, W]
        
        # 3b. 构建增强输入
        consistency_input = torch.stack([
            high_consistency, 
            low_consistency, 
            explicit_diff
        ], dim=1)  # [B, H, W, 3]

        # 3c. 对比网络融合
        contrast_scores = self.contrast_net(consistency_input)
        # 3d. 生成注意力图
        attention_map = self.attention_head(contrast_scores)  # [B, 1, H, W]

        return attention_map