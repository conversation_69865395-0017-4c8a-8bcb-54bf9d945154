import torch
import torch.nn as nn
import torch.nn.functional as F


class CrossAttentionVoxelFilter(nn.Module):
    """
    跨注意力体素特征筛选器

    使用模板特征作为query，体素特征作为key/value进行跨注意力计算
    得到筛选和增强后的体素特征，比简单的相似度图更加精细
    """

    def __init__(self, motion_channels, embed_dim, num_heads=8, temperature=1.0):
        super().__init__()
        self.motion_channels = motion_channels
        self.embed_dim = embed_dim
        self.num_heads = num_heads
        self.temperature = temperature
        self.head_dim = motion_channels // num_heads

        assert motion_channels % num_heads == 0, "motion_channels must be divisible by num_heads"

        # 模板特征投射为查询
        self.template_to_query = nn.Linear(embed_dim, motion_channels)

        # 体素特征投射为键值
        self.voxel_to_key = nn.Conv2d(motion_channels, motion_channels, 1)
        self.voxel_to_value = nn.Conv2d(motion_channels, motion_channels, 1)

        # 输出投射
        self.output_proj = nn.Conv2d(motion_channels, motion_channels, 1)

        # 归一化层
        self.norm = nn.LayerNorm(motion_channels)

    def forward(self, motion_features, template_features):
        """
        跨注意力体素特征筛选

        Args:
            motion_features: [B, motion_channels, H, W] 体素运动特征
            template_features: [B, embed_dim, H_z, W_z] 模板特征

        Returns:
            filtered_voxel_features: [B, motion_channels, H, W] 筛选后的体素特征
            attention_weights: [B, num_heads, H*W, H_z*W_z] 注意力权重
        """
        B, C, H, W = motion_features.shape
        _, E, H_z, W_z = template_features.shape

        # 1. 准备查询（来自模板特征）
        template_flat = template_features.view(B, E, -1).transpose(1, 2)  # [B, H_z*W_z, E]
        query = self.template_to_query(template_flat)  # [B, H_z*W_z, C]
        query = query.view(B, H_z*W_z, self.num_heads, self.head_dim).transpose(1, 2)  # [B, num_heads, H_z*W_z, head_dim]

        # 2. 准备键值（来自体素特征）
        key = self.voxel_to_key(motion_features)  # [B, C, H, W]
        value = self.voxel_to_value(motion_features)  # [B, C, H, W]

        key_flat = key.view(B, C, -1).transpose(1, 2)  # [B, H*W, C]
        value_flat = value.view(B, C, -1).transpose(1, 2)  # [B, H*W, C]

        key = key_flat.view(B, H*W, self.num_heads, self.head_dim).transpose(1, 2)  # [B, num_heads, H*W, head_dim]
        value = value_flat.view(B, H*W, self.num_heads, self.head_dim).transpose(1, 2)  # [B, num_heads, H*W, head_dim]

        # 3. 计算跨注意力
        # query: [B, num_heads, H_z*W_z, head_dim]
        # key: [B, num_heads, H*W, head_dim]
        attention_scores = torch.matmul(query, key.transpose(-2, -1))  # [B, num_heads, H_z*W_z, H*W]
        attention_scores = attention_scores / (self.head_dim ** 0.5) / self.temperature
        attention_weights = F.softmax(attention_scores, dim=-1)  # [B, num_heads, H_z*W_z, H*W]

        # 4. 应用注意力到体素特征
        # attention_weights: [B, num_heads, H_z*W_z, H*W]
        # value: [B, num_heads, H*W, head_dim]
        attended_features = torch.matmul(attention_weights.transpose(-2, -1), query)  # [B, num_heads, H*W, head_dim]

        # 5. 重塑和投射输出
        attended_features = attended_features.transpose(1, 2).contiguous()  # [B, H*W, num_heads, head_dim]
        attended_features = attended_features.view(B, H*W, C)  # [B, H*W, C]

        # 归一化
        attended_features = self.norm(attended_features)

        # 重塑回空间维度
        attended_features = attended_features.transpose(1, 2).view(B, C, H, W).contiguous()  # [B, C, H, W]

        # 输出投射
        filtered_features = self.output_proj(attended_features)

        # 残差连接
        filtered_features = motion_features + filtered_features

        return filtered_features, attention_weights


class PromptMotionInjector(nn.Module):
    """
    Prompt运动注入器

    核心功能：
    1. 跨注意力体素特征筛选（唯一筛选机制）
    2. 直接特征融合
    3. 双重置信度自适应权重应用

    工作流程：
    - 跨注意力：模板引导的精细特征筛选
    - 直接融合：筛选特征与prompt直接融合
    - 双重置信度：运动强度 + 跨注意力置信度

    技术特点：
    - 使用GELU激活函数减少死神经元
    - 8头跨注意力提供丰富的特征交互
    - 单一筛选机制，避免冗余计算
    - 简洁高效的设计，专注于跨注意力筛选
    """
    
    def __init__(self, embed_dim=768, motion_channels=256):
        super().__init__()
        self.embed_dim = embed_dim
        self.motion_channels = motion_channels

        # 1. 跨注意力体素特征筛选器（唯一筛选机制）
        self.cross_attention_filter = CrossAttentionVoxelFilter(
            motion_channels=motion_channels,
            embed_dim=embed_dim,
            num_heads=8,
            temperature=1.5
        )

        # 2. 直接特征融合网络（使用GELU替代ReLU，减少死神经元）
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(embed_dim + motion_channels, embed_dim, 3, padding=1),
            nn.GELU(),  # 使用GELU替代ReLU
            nn.Conv2d(embed_dim, embed_dim, 1)  # 最后一层不需要激活，因为是残差连接
        )

        # 3. 自适应权重网络（基于运动强度和跨注意力置信度）
        self.base_alpha = 0.2
        self.adaptive_weight = nn.Sequential(
            nn.Linear(2, 8),  # 输入：运动强度 + 跨注意力置信度
            nn.GELU(),  # 使用GELU替代ReLU
            nn.Linear(8, 1),
            nn.Sigmoid()  # 保留Sigmoid确保权重在[0,1]范围
        )
        
    def forward(self, processed_motion_features: torch.Tensor, 
                motion_intensity: torch.Tensor,
                template_features: torch.Tensor, 
                prompt: torch.Tensor) -> torch.Tensor:
        """
        Prompt运动注入
        
        Args:
            processed_motion_features: [B, motion_channels, H, W] 预处理的运动特征
            motion_intensity: [B] 运动强度评分
            template_features: [B, embed_dim, H_z, W_z] 模板特征
            prompt: [B, embed_dim, H, W] prompt特征
            
        Returns:
            enhanced_prompt: [B, embed_dim, H, W] 增强后的prompt特征
        """
        B = prompt.shape[0]
        H, W = prompt.shape[-2:]
        
        # 1. 确保运动特征空间尺寸匹配
        if processed_motion_features.shape[-2:] != (H, W):
            processed_motion_features = F.interpolate(
                processed_motion_features, size=(H, W),
                mode='bilinear', align_corners=False
            )
        
        # 2. 跨注意力体素特征筛选（唯一筛选机制）
        filtered_motion_features, cross_attention_weights = self.cross_attention_filter(
            processed_motion_features, template_features
        )

        # 3. 直接特征融合（使用筛选后的特征）
        combined_features = torch.cat([prompt, filtered_motion_features], dim=1)  # [B, embed_dim + motion_channels, H, W]
        modifications = self.feature_fusion(combined_features)  # [B, embed_dim, H, W]

        # 4. 计算自适应权重（基于运动强度和跨注意力置信度）
        cross_attention_confidence = torch.mean(cross_attention_weights.max(dim=-1)[0], dim=[1, 2])  # [B]

        weight_input = torch.stack([motion_intensity, cross_attention_confidence], dim=1)  # [B, 2]
        adaptive_weight = self.adaptive_weight(weight_input).view(B, 1, 1, 1)  # [B, 1, 1, 1]
        adaptive_weight = self.base_alpha + (1 - self.base_alpha) * adaptive_weight

        # 5. 应用修改
        enhanced_prompt = prompt + modifications * adaptive_weight

        return enhanced_prompt
