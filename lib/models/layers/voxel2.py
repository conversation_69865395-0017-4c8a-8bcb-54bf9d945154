import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import fps, knn, global_max_pool
from torch_geometric.nn.conv import PointNetConv
from torch_geometric.utils import to_dense_batch

class VoxelFeatureExtractor(nn.Module):
    """
    一个对输入顺序不敏感的模块，用于从16个极性事件中提取特征。
    它直接将核心统计量作为输入，让一个简单的MLP去学习它们之间的非线性关系。
    """
    def __init__(self, feature_dim=64):
        super().__init__()
        
        # 输入一个MLP
        self.feature_encoder = nn.Sequential(
            nn.Linear(5, 32), # 输入5个核心统计量
            nn.ReLU(inplace=True),
            nn.Linear(32, feature_dim) # 直接输出最终维度
        )

    def forward(self, polarities):
        """
        输入: polarities [B, N, 16]
        """
        # --- 计算核心统计特征 ---
        pos_counts = (polarities > 0).float().sum(dim=-1, keepdim=True)
        neg_counts = (polarities < 0).float().sum(dim=-1, keepdim=True)
        total_counts = pos_counts + neg_counts
        
        # 计算平衡度（方向性）和正事件比例
        balance = pos_counts - neg_counts
        pos_ratio = pos_counts / (total_counts + 1e-6)
        
        # 组合5个核心统计量
        stats = torch.cat([
            total_counts, 
            pos_counts, 
            neg_counts, 
            balance, 
            pos_ratio
        ], dim=-1)

        final_features = self.feature_encoder(stats)  # [B, N, feature_dim]

        return final_features


class SpatioTemporalEncoderLayer(nn.Module):
    """
    单个Transformer编码器层。
    增加了对相对位置编码的支持。
    """
    def __init__(self, feature_dim=128, num_heads=4):
        super().__init__()
        self.attention = nn.MultiheadAttention(
            embed_dim=feature_dim,
            num_heads=num_heads,
            batch_first=True,
            dropout=0.1
        )
        self.ffn = nn.Sequential(
            nn.Linear(feature_dim, feature_dim * 2),
            nn.GELU(),
            nn.Linear(feature_dim * 2, feature_dim)
        )
        self.norm1 = nn.LayerNorm(feature_dim)
        self.norm2 = nn.LayerNorm(feature_dim)

        # 用于将相对坐标差映射为注意力偏置的小型MLP
        self.relative_pos_embed = nn.Sequential(
            nn.Linear(3, 64), # 输入(dx, dy, dt)
            nn.ReLU(),
            nn.Linear(64, num_heads) # 输出每个头的偏置
        )

    def forward(self, features, coords, padding_mask=None):
        # --- 计算相对位置偏置 ---
        # coords: [B, N, 3] -> [B, N, 1, 3] - [B, 1, N, 3] = [B, N, N, 3]
        relative_coords = coords.unsqueeze(2) - coords.unsqueeze(1)
        # 通过MLP得到注意力偏置: [B, N, N, num_heads]
        relative_pos_bias = self.relative_pos_embed(relative_coords.float())
        # 调整维度以匹配MHA的attn_mask: [B*num_heads, N, N]
        relative_pos_bias = relative_pos_bias.permute(0, 3, 1, 2).flatten(0, 1)

        # Self-Attention
        attn_output, _ = self.attention(features, features, features, 
                                        key_padding_mask=padding_mask,
                                        attn_mask=relative_pos_bias)
        
        x = self.norm1(features + attn_output)
        ffn_output = self.ffn(x)
        output_features = self.norm2(x + ffn_output)
        
        return output_features
    
# ------------------------------------------------------------------------------
# 2. 层级式点云处理模块
# ------------------------------------------------------------------------------

class HierarchicalVoxelEncoder(nn.Module):
    """
    【批处理版】层级式体素编码器。
    该版本将整个批次的有效点合并处理，以利用并行化优势。
    """
    def __init__(self, in_dim=32, out_dim=768):
        super().__init__()
        
        # Stage 1: ~4096 -> 1024
        mlp1 = nn.Sequential(
            nn.Linear(in_dim + 3, in_dim * 2),
            nn.ReLU(inplace=True),
            nn.Linear(in_dim * 2, in_dim * 4)
        )
        self.sa1_conv = PointNetConv(local_nn=mlp1)
        self.sa1_npoint = 1024
        self.sa1_nsample = 16

        # Stage 2: 1024 -> 512
        mlp2 = nn.Sequential(
            nn.Linear(in_dim * 4 + 3, in_dim * 8),
            nn.ReLU(inplace=True),
            nn.Linear(in_dim * 8, in_dim * 16)
        )
        self.sa2_conv = PointNetConv(local_nn=mlp2)
        self.sa2_npoint = 512
        self.sa2_nsample = 32

        # Stage 3: 512 -> 256 (最终输出)
        mlp3 = nn.Sequential(
            nn.Linear(in_dim * 16 + 3, in_dim * 16),
            nn.ReLU(inplace=True),
            nn.Linear(in_dim * 16, out_dim)
        )
        self.sa3_conv = PointNetConv(local_nn=mlp3)
        self.sa3_npoint = 256
        self.sa3_nsample = 32


    def forward(self, xyz, features, valid_mask):
        """
        Input:
            xyz: [B, N, 3]
            features: [B, N, D]
            valid_mask: [B, N] bool, 指示哪些是有效点
        """
        B, N, C_feat = features.shape
        
        # --- 1. 将批处理数据扁平化并过滤无效点 ---
        flat_valid_mask = valid_mask.view(-1)
        
        # 创建批次索引: [0,0,...,0, 1,1,...,1, ..., B-1]
        batch_idx = torch.arange(B, device=xyz.device, dtype=torch.long).view(B, 1).expand(B, N).reshape(-1)
        
        # 过滤出有效点和对应的批次索引
        valid_xyz = xyz.view(-1, 3)[flat_valid_mask]
        valid_features = features.view(-1, C_feat)[flat_valid_mask]
        valid_batch_idx = batch_idx[flat_valid_mask]

        # 如果整个批次都没有有效点，直接返回零张量
        if valid_xyz.size(0) == 0:
            final_out_dim = self.sa3_conv.local_nn[-1].out_features
            return torch.zeros(B, self.sa3_npoint, 3, device=xyz.device), \
                   torch.zeros(B, self.sa3_npoint, final_out_dim, device=xyz.device)

        # --- Stage 1 ---
        num_total_valid = valid_xyz.size(0)
        s1_ratio = (B * self.sa1_npoint) / num_total_valid if num_total_valid > 0 else 1.0
        s1_ratio = min(1.0, s1_ratio)
        fps_idx_1 = fps(valid_xyz, valid_batch_idx, ratio=s1_ratio, random_start=False)
        
        s1_center_xyz = valid_xyz[fps_idx_1]
        s1_center_batch_idx = valid_batch_idx[fps_idx_1]

        edge_index_1 = knn(valid_xyz, s1_center_xyz, k=self.sa1_nsample, batch_x=valid_batch_idx, batch_y=s1_center_batch_idx)
        num_centers_s1 = s1_center_xyz.size(0)
        valid_edge_mask_s1 = edge_index_1[1] < num_centers_s1
        if not valid_edge_mask_s1.all():
            edge_index_1 = edge_index_1[:, valid_edge_mask_s1]
        
        s1_center_features = self.sa1_conv(x=(valid_features, None), pos=(valid_xyz, s1_center_xyz), edge_index=edge_index_1)

        # --- Stage 2 ---
        num_s1_total = s1_center_xyz.size(0)
        s2_ratio = (B * self.sa2_npoint) / num_s1_total if num_s1_total > 0 else 1.0
        s2_ratio = min(1.0, s2_ratio)
        fps_idx_2 = fps(s1_center_xyz, s1_center_batch_idx, ratio=s2_ratio, random_start=False)

        s2_center_xyz = s1_center_xyz[fps_idx_2]
        s2_center_batch_idx = s1_center_batch_idx[fps_idx_2]

        edge_index_2 = knn(s1_center_xyz, s2_center_xyz, k=self.sa2_nsample, batch_x=s1_center_batch_idx, batch_y=s2_center_batch_idx)
        num_centers_s2 = s2_center_xyz.size(0)
        valid_edge_mask_s2 = edge_index_2[1] < num_centers_s2
        if not valid_edge_mask_s2.all():
            edge_index_2 = edge_index_2[:, valid_edge_mask_s2]
        
        s2_center_features = self.sa2_conv(x=(s1_center_features, None), pos=(s1_center_xyz, s2_center_xyz), edge_index=edge_index_2)

        # --- Stage 3 ---
        num_s2_total = s2_center_xyz.size(0)
        s3_ratio = (B * self.sa3_npoint) / num_s2_total if num_s2_total > 0 else 1.0
        s3_ratio = min(1.0, s3_ratio)
        fps_idx_3 = fps(s2_center_xyz, s2_center_batch_idx, ratio=s3_ratio, random_start=False)

        s3_center_xyz = s2_center_xyz[fps_idx_3]
        s3_center_batch_idx = s2_center_batch_idx[fps_idx_3]

        edge_index_3 = knn(s2_center_xyz, s3_center_xyz, k=self.sa3_nsample, batch_x=s2_center_batch_idx, batch_y=s3_center_batch_idx)
        num_centers_s3 = s3_center_xyz.size(0)
        valid_edge_mask_s3 = edge_index_3[1] < num_centers_s3
        if not valid_edge_mask_s3.all():
            edge_index_3 = edge_index_3[:, valid_edge_mask_s3]

        final_flat_features = self.sa3_conv(x=(s2_center_features, None), pos=(s2_center_xyz, s3_center_xyz), edge_index=edge_index_3)

        # --- 将扁平化的数据重新组织为Batch ---
        final_xyz, _ = to_dense_batch(s3_center_xyz, s3_center_batch_idx, fill_value=0, max_num_nodes=self.sa3_npoint, batch_size=B)
        final_features, _ = to_dense_batch(final_flat_features, s3_center_batch_idx, fill_value=0, max_num_nodes=self.sa3_npoint, batch_size=B)

        return final_xyz, final_features


# ------------------------------------------------------------------------------
# 3. 跨模态融合与主模块
# ------------------------------------------------------------------------------

class CrossAttentionFusion(nn.Module):
    def __init__(self, embed_dim=768, num_heads=8, search_feat_size=16):
        super().__init__()
        self.cross_attn = nn.MultiheadAttention(
            embed_dim=embed_dim,
            num_heads=num_heads,
            batch_first=True
        )
        # 为图像块创建可学习的位置嵌入 (16*16=256)
        self.img_pos_embed = nn.Embedding(search_feat_size * search_feat_size, embed_dim)
        # 为体素块的2D坐标创建位置嵌入
        self.voxel_2d_pos_embed = nn.Sequential(
            nn.Linear(2, 128),
            nn.ReLU(),
            nn.Linear(128, embed_dim)
        )
        self.motion_intensity = nn.Sequential(
            nn.Linear(embed_dim, 64), nn.ReLU(), nn.Linear(64, 1), nn.Sigmoid()
        )
        self.motion_consistency = nn.Sequential(
            nn.Linear(embed_dim, 32), nn.ReLU(), nn.Linear(32, 1), nn.Sigmoid()
        )
        self.fusion = nn.Sequential(
            nn.Linear(embed_dim, embed_dim), nn.LayerNorm(embed_dim), nn.GELU(), nn.Dropout(0.1)
        )
        self.alpha_predictor = nn.Sequential(
            nn.Linear(embed_dim + 2, 64), nn.ReLU(), nn.Linear(64, 1), nn.Sigmoid()
        )
        self.base_alpha = 0.05
        self.alpha_scale = 0.8

    def forward(self, search_features, motion_features, motion_coords):
        # 位置嵌入
        B, L_s, C = search_features.shape
        img_pos_ids = torch.arange(L_s, device=search_features.device).view(1, -1)
        search_features_pos = search_features + self.img_pos_embed(img_pos_ids)
        motion_coords_xy = motion_coords[:, :, :2].float()
        voxel_pos = self.voxel_2d_pos_embed(motion_coords_xy)
        motion_features_pos = motion_features + voxel_pos

        motion_context, _ = self.cross_attn(
            query=search_features_pos, key=motion_features_pos, value=motion_features
        )
        motion_global = motion_features.mean(dim=1)
        motion_int = self.motion_intensity(motion_global)
        motion_con = self.motion_consistency(motion_global)
        dynamic_factor = torch.cat([motion_global, motion_int, motion_con], dim=1)
        alpha = self.alpha_predictor(dynamic_factor)
        alpha = self.base_alpha + self.alpha_scale * alpha * motion_int
        alpha = alpha.unsqueeze(1)
        enhanced_features = search_features + alpha * self.fusion(motion_context)
        motion_info = {'alpha': alpha.mean().item(), 'intensity': motion_int.mean().item()}
        return enhanced_features, motion_info
    
class VoxelMotionEnhancer(nn.Module):
    def __init__(self, embed_dim=768, num_heads=8):
        super().__init__()
        feature_dim = 32

        self.event_voxel_encoder = VoxelFeatureExtractor(feature_dim=feature_dim)
        self.hierarchical_encoder = HierarchicalVoxelEncoder(in_dim=feature_dim, out_dim=embed_dim)
        self.cross_attn_fusion = CrossAttentionFusion(embed_dim=embed_dim, num_heads=num_heads)

    def process_voxels(self, voxel_data):
        voxel_data = voxel_data.squeeze(1).float()
        coords = voxel_data[:, :, :3]
        polarities = voxel_data[:, :, 3:]

        # --- 关键：创建有效点掩码，过滤填充的零点 ---
        # 坐标绝对值之和大于一个很小的值，则认为是有效点
        valid_mask = coords.abs().sum(dim=-1) > 1e-6

        # 1. 提取每个体素的独立特征
        voxel_features = self.event_voxel_encoder(polarities)

        # 2. 通过层级编码器进行特征提取与对齐 (只处理有效点)
        aligned_coords, motion_tokens = self.hierarchical_encoder(coords, voxel_features, valid_mask)
        
        return motion_tokens, aligned_coords

    def forward(self, search_features, search_voxels):
        """
        :param search_features: [B, 256, 768] 来自图像主干
        :param search_voxels: [B, 1, 1, 4096, 19]
        """
        # 处理体素数据，得到对齐后的运动token
        search_motion_tokens, align_coords = self.process_voxels(search_voxels)

        # 使用交叉注意力将运动信息融合到图像特征中
        enhanced_features, motion_info = self.cross_attn_fusion(search_features, search_motion_tokens, align_coords)

        return enhanced_features, motion_info