import numpy as np
from lib.test.evaluation.data import Sequence, BaseDataset, SequenceList
from lib.test.utils.load_text import load_text
import os


class VTUAVDataset(BaseDataset):

    def __init__(self, subset):
        super().__init__()
        # subset can be short-term, long-term
        if subset == 'st':
            self.base_path = os.path.join(self.env_settings.vtuav_path, 'short-term')
        elif subset == 'lt':
            self.base_path = os.path.join(self.env_settings.vtuav_path, 'long-term')
        else:
            raise ValueError(f"No {subset} subset in VTUAV TEST!")
        self.sequence_list = self._get_sequence_list(subset)
        self.subset = subset

    def get_sequence_list(self):
        return SequenceList([self._construct_sequence(s) for s in self.sequence_list])

    def _construct_sequence(self, sequence_name):
        anno_path_rgb = '{}/{}/rgb.txt'.format(self.base_path, sequence_name)
        anno_path_x = '{}/{}/ir.txt'.format(self.base_path, sequence_name)

        ground_truth_rect_rgb = load_text(str(anno_path_rgb), delimiter=' ', dtype=np.float64)
        ground_truth_rect_x = load_text(str(anno_path_x), delimiter=' ', dtype=np.float64)
        ground_truth_rect = np.concatenate([ground_truth_rect_rgb, ground_truth_rect_x], axis=1)
        # /mnt/6196b16a-836e-45a4-b6f2-641dca0991d0/VTUAV/test/short-term/test_ST_001/animal_001/rgb/000001.jpg
        rgb_frames_path = '{}/{}/{}'.format(self.base_path, sequence_name, 'rgb')
        rgb_frame_list = sorted([frame for frame in os.listdir(rgb_frames_path) if frame.endswith(".jpg")])
        # rgb_frame_list.sort(key=lambda f: int(f.split('.')[0]))
        rgb_frames_list = [os.path.join(rgb_frames_path, frame) for frame in rgb_frame_list]

        x_frames_path = '{}/{}/{}'.format(self.base_path, sequence_name, 'ir')
        x_frame_list = sorted([frame for frame in os.listdir(x_frames_path) if frame.endswith(".jpg")])
        # x_frame_list.sort(key=lambda f: int(f.split('.')[0]))
        x_frames_list = [os.path.join(x_frames_path, frame) for frame in x_frame_list]
        frames_list = list(zip(rgb_frames_list, x_frames_list))

        return Sequence(sequence_name, frames_list, 'vtuav_{}'.format(self.subset), ground_truth_rect.reshape(-1, 8))

    def __len__(self):
        return len(self.sequence_list)

    def _get_sequence_list(self, subset):
        if subset == 'st':    # #176
            sequence_list = [
                'test_ST_001/animal_001',
                'test_ST_001/bike_003',
                'test_ST_001/bike_005',
                'test_ST_001/bike_006',
                'test_ST_001/bike_008',
                'test_ST_001/bus_001',
                'test_ST_001/bus_004',
                'test_ST_001/bus_006',
                'test_ST_001/bus_007',
                'test_ST_001/bus_026',
                'test_ST_001/bus_028',
                'test_ST_002/bus_029',
                'test_ST_002/c-vehicle_003',
                'test_ST_002/cable_002',
                'test_ST_002/car_004',
                'test_ST_002/car_005',
                'test_ST_002/car_006',
                'test_ST_002/car_007',
                'test_ST_002/car_012',
                'test_ST_002/car_020',
                'test_ST_002/car_022',
                'test_ST_002/car_027',
                'test_ST_002/car_042',
                'test_ST_002/car_049',
                'test_ST_002/car_053',
                'test_ST_002/car_056',
                'test_ST_002/car_059',
                'test_ST_003/car_060',
                'test_ST_003/car_061',
                'test_ST_003/car_063',
                'test_ST_003/car_064',
                'test_ST_003/car_065',
                'test_ST_003/car_067',
                'test_ST_003/car_072',
                'test_ST_003/car_075',
                'test_ST_003/car_077',
                'test_ST_003/car_079',
                'test_ST_003/car_096',
                'test_ST_003/car_097',
                'test_ST_003/car_101',
                'test_ST_003/car_106',
                'test_ST_003/car_109',
                'test_ST_003/car_110',
                'test_ST_004/bus_014',
                'test_ST_004/bus_019',
                'test_ST_004/bus_021',
                'test_ST_004/car_112',
                'test_ST_004/car_123',
                'test_ST_004/car_128',
                'test_ST_004/car_129',
                'test_ST_004/car_132',
                'test_ST_004/elebike_002',
                'test_ST_004/elebike_004',
                'test_ST_004/elebike_005',
                'test_ST_004/elebike_006',
                'test_ST_004/elebike_007',
                'test_ST_004/elebike_008',
                'test_ST_004/elebike_010',
                'test_ST_004/elebike_011',
                'test_ST_004/elebike_018',
                'test_ST_004/elebike_019',
                'test_ST_004/tricycle_027',
                'test_ST_004/tricycle_032',
                'test_ST_004/tricycle_035',
                'test_ST_004/tricycle_037',
                'test_ST_004/truck_004',
                'test_ST_004/truck_007',
                'test_ST_004/truck_008',
                'test_ST_005/excavator_001',
                'test_ST_005/pedestrian_001',
                'test_ST_005/pedestrian_005',
                'test_ST_005/pedestrian_006',
                'test_ST_005/pedestrian_007',
                'test_ST_005/pedestrian_010',
                'test_ST_005/pedestrian_015',
                'test_ST_005/pedestrian_016',
                'test_ST_005/pedestrian_017',
                'test_ST_006/pedestrian_038',
                'test_ST_006/pedestrian_041',
                'test_ST_006/pedestrian_044',
                'test_ST_006/pedestrian_046',
                'test_ST_006/pedestrian_050',
                'test_ST_006/pedestrian_051',
                'test_ST_006/pedestrian_052',
                'test_ST_006/pedestrian_053',
                'test_ST_006/pedestrian_056',
                'test_ST_006/pedestrian_058',
                'test_ST_006/pedestrian_060',
                'test_ST_006/pedestrian_062',
                'test_ST_006/pedestrian_064',
                'test_ST_007/pedestrian_077',
                'test_ST_007/pedestrian_079',
                'test_ST_007/pedestrian_080',
                'test_ST_007/pedestrian_088',
                'test_ST_007/pedestrian_089',
                'test_ST_007/pedestrian_093',
                'test_ST_007/pedestrian_095',
                'test_ST_007/pedestrian_098',
                'test_ST_007/pedestrian_109',
                'test_ST_007/pedestrian_110',
                'test_ST_007/pedestrian_111',
                'test_ST_007/pedestrian_112',
                'test_ST_007/pedestrian_113',
                'test_ST_007/pedestrian_117',
                'test_ST_007/pedestrian_119',
                'test_ST_007/pedestrian_120',
                'test_ST_007/pedestrian_121',
                'test_ST_007/pedestrian_122',
                'test_ST_007/pedestrian_123',
                'test_ST_007/pedestrian_127',
                'test_ST_007/pedestrian_130',
                'test_ST_007/pedestrian_134',
                'test_ST_007/pedestrian_136',
                'test_ST_007/pedestrian_138',
                'test_ST_008/pedestrian_139',
                'test_ST_008/pedestrian_142',
                'test_ST_008/pedestrian_143',
                'test_ST_008/pedestrian_148',
                'test_ST_008/pedestrian_149',
                'test_ST_008/pedestrian_150',
                'test_ST_008/pedestrian_151',
                'test_ST_008/pedestrian_152',
                'test_ST_008/pedestrian_153',
                'test_ST_008/pedestrian_154',
                'test_ST_009/pedestrian_173',
                'test_ST_009/pedestrian_179',
                'test_ST_009/pedestrian_183',
                'test_ST_009/pedestrian_185',
                'test_ST_009/pedestrian_192',
                'test_ST_009/pedestrian_195',
                'test_ST_009/pedestrian_196',
                'test_ST_009/pedestrian_209',
                'test_ST_009/pedestrian_211',
                'test_ST_009/pedestrian_213',
                'test_ST_009/pedestrian_215',
                'test_ST_010/ship_001',
                'test_ST_010/train_003',
                'test_ST_010/train_004',
                'test_ST_010/tricycle_003',
                'test_ST_010/tricycle_004',
                'test_ST_010/tricycle_005',
                'test_ST_010/tricycle_006',
                'test_ST_010/tricycle_007',
                'test_ST_010/tricycle_008',
                'test_ST_010/tricycle_009',
                'test_ST_010/tricycle_010',
                'test_ST_010/tricycle_011',
                'test_ST_010/tricycle_016',
                'test_ST_010/tricycle_017',
                'test_ST_010/tricycle_019',
                'test_ST_010/tricycle_023',
                'test_ST_011/pedestrian_162',
                'test_ST_011/pedestrian_163',
                'test_ST_011/pedestrian_164',
                'test_ST_011/pedestrian_217',
                'test_ST_011/pedestrian_227',
                'test_ST_011/pedestrian_229',
                'test_ST_011/pedestrian_230',
                'test_ST_011/pedestrian_234',
                'test_ST_012/pedestrian_023',
                'test_ST_012/pedestrian_025',
                'test_ST_012/pedestrian_026',
                'test_ST_012/pedestrian_155',
                'test_ST_012/pedestrian_156',
                'test_ST_012/pedestrian_161',
                'test_ST_013/bus_010',
                'test_ST_013/bus_012',
                'test_ST_013/elebike_031',
                'test_ST_013/elebike_032',
                'test_ST_013/pedestrian_019',
                'test_ST_013/pedestrian_020',
                'test_ST_013/pedestrian_027',
                'test_ST_013/pedestrian_028',
                'test_ST_013/pedestrian_033',
                'test_ST_013/pedestrian_034',
                'test_ST_013/pedestrian_036'
            ]
        elif subset == 'lt':  # #74
            sequence_list = [
                'test_LT_001/bus_025',
                'test_LT_001/bus_032',
                'test_LT_001/car_001',
                'test_LT_001/car_008',
                'test_LT_001/car_054',
                'test_LT_001/car_055',
                'test_LT_001/car_057',
                'test_LT_001/car_070',
                'test_LT_002/animal_003',
                'test_LT_002/animal_004',
                'test_LT_002/bike_001',
                'test_LT_002/car_010',
                'test_LT_002/car_015',
                'test_LT_002/car_018',
                'test_LT_002/car_036',
                'test_LT_002/car_046',
                'test_LT_003/car_073',
                'test_LT_003/car_091',
                'test_LT_003/car_095',
                'test_LT_003/car_103',
                'test_LT_003/car_119',
                'test_LT_003/car_125',
                'test_LT_003/car_127',
                'test_LT_003/elebike_003',
                'test_LT_004/elebike_009',
                'test_LT_004/elebike_012',
                'test_LT_004/elebike_013',
                'test_LT_004/elebike_014',
                'test_LT_004/elebike_027',
                'test_LT_004/elebike_029',
                'test_LT_004/pedestrian_002',
                'test_LT_004/pedestrian_009',
                'test_LT_004/pedestrian_013',
                'test_LT_004/pedestrian_014',
                'test_LT_004/pedestrian_021',
                'test_LT_004/pedestrian_024',
                'test_LT_005/pedestrian_037',
                'test_LT_005/pedestrian_055',
                'test_LT_005/pedestrian_132',
                'test_LT_005/pedestrian_137',
                'test_LT_005/pedestrian_140',
                'test_LT_005/pedestrian_141',
                'test_LT_005/pedestrian_144',
                'test_LT_005/pedestrian_145',
                'test_LT_006/pedestrian_168',
                'test_LT_006/pedestrian_178',
                'test_LT_006/pedestrian_182',
                'test_LT_006/pedestrian_184',
                'test_LT_006/pedestrian_187',
                'test_LT_006/pedestrian_188',
                'test_LT_006/pedestrian_190',
                'test_LT_006/pedestrian_194',
                'test_LT_007/pedestrian_208',
                'test_LT_007/pedestrian_220',
                'test_LT_007/pedestrian_221',
                'test_LT_008/pedestrian_214',
                'test_LT_008/pedestrian_218',
                'test_LT_008/pedestrian_219',
                'test_LT_009/pedestrian_199',
                'test_LT_009/pedestrian_201',
                'test_LT_009/pedestrian_204',
                'test_LT_009/pedestrian_205',
                'test_LT_009/tricycle_002',
                'test_LT_009/tricycle_012',
                'test_LT_009/tricycle_013',
                'test_LT_009/tricycle_015',
                'test_LT_009/tricycle_018',
                'test_LT_009/tricycle_025',
                'test_LT_009/tricycle_036',
                'test_LT_009/truck_001',
                'test_LT_010/pedestrian_207',
                'test_LT_010/pedestrian_212',
                'test_LT_010/pedestrian_226',
                'test_LT_010/pedestrian_232'
            ]
        else:
            raise ValueError(f'VTUAV has no {subset} subset')
        return sequence_list
