nohup: 忽略输入
W0728 20:18:58.269634 98778 site-packages/torch/distributed/run.py:793] 
W0728 20:18:58.269634 98778 site-packages/torch/distributed/run.py:793] *****************************************
W0728 20:18:58.269634 98778 site-packages/torch/distributed/run.py:793] Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
W0728 20:18:58.269634 98778 site-packages/torch/distributed/run.py:793] *****************************************
Process 98836: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
Process 98838: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
0
2
script_name: vipt.py  config_name: coesot.yaml
Process 98837: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
Process 98839: CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
1
3
script_name: vipt.py  config_name: coesot.yaml
script_name: vipt.py  config_name: coesot.yaml
script_name: vipt.py  config_name: coesot.yaml
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
New configuration is shown below.
MODEL configuration: {'PRETRAIN_FILE': './pretrained/OSTrack_coesot.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}


TRAIN configuration: {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0004, 'WEIGHT_DECAY': 5e-05, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 5, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 1e-05}}


DATA configuration: {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['COESOT'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['COESOT_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 40000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}


TEST configuration: {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 5}


sampler_mode causal
sampler_mode causal
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
sampler_mode causal
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
sampler_mode causal
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
Load pretrained model from: ./pretrained/OSTrack_coesot.pth.tar
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints
No matching checkpoint file found
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints
Learnable parameters are shown below.
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints
module.backbone.voxel_extractor.conv3d.0.weight
module.backbone.voxel_extractor.conv3d.0.bias
module.backbone.voxel_extractor.conv3d.1.weight
module.backbone.voxel_extractor.conv3d.1.bias
module.backbone.voxel_extractor.conv3d.3.weight
module.backbone.voxel_extractor.conv3d.3.bias
module.backbone.voxel_extractor.conv3d.4.weight
module.backbone.voxel_extractor.conv3d.4.bias
module.backbone.voxel_extractor.conv3d.7.weight
module.backbone.voxel_extractor.conv3d.7.bias
module.backbone.voxel_extractor.conv3d.8.weight
module.backbone.voxel_extractor.conv3d.8.bias
module.backbone.voxel_extractor.conv3d.10.weight
module.backbone.voxel_extractor.conv3d.10.bias
module.backbone.voxel_extractor.conv3d.11.weight
module.backbone.voxel_extractor.conv3d.11.bias
module.backbone.voxel_extractor.motion_direction.0.weight
module.backbone.voxel_extractor.motion_direction.0.bias
module.backbone.voxel_extractor.motion_direction.1.weight
module.backbone.voxel_extractor.motion_direction.1.bias
module.backbone.voxel_extractor.motion_direction.2.weight
module.backbone.voxel_extractor.motion_direction.2.bias
module.backbone.voxel_extractor.temporal_diff.0.weight
module.backbone.voxel_extractor.temporal_diff.0.bias
module.backbone.voxel_extractor.feature_fusion.0.weight
module.backbone.voxel_extractor.feature_fusion.0.bias
module.backbone.voxel_extractor.feature_fusion.1.weight
module.backbone.voxel_extractor.feature_fusion.1.bias
module.backbone.voxel_extractor.projection.0.weight
module.backbone.voxel_extractor.projection.0.bias
module.backbone.voxel_extractor.projection.1.weight
module.backbone.voxel_extractor.projection.1.bias
module.backbone.voxel_attn.cross_attn.in_proj_weight
module.backbone.voxel_attn.cross_attn.in_proj_bias
module.backbone.voxel_attn.cross_attn.out_proj.weight
module.backbone.voxel_attn.cross_attn.out_proj.bias
module.backbone.voxel_attn.fusion.0.weight
module.backbone.voxel_attn.fusion.0.bias
module.backbone.voxel_attn.fusion.1.weight
module.backbone.voxel_attn.fusion.1.bias
module.backbone.voxel_attn.alpha_predictor.0.weight
module.backbone.voxel_attn.alpha_predictor.0.bias
module.backbone.voxel_attn.alpha_predictor.2.weight
module.backbone.voxel_attn.alpha_predictor.2.bias
checkpoints will be saved to /home/<USER>/STU/workspaces/ruihui/ViPT/output/checkpoints
No matching checkpoint file found
No matching checkpoint file found
No matching checkpoint file found
[train: 1, 50 / 234] FPS: 7.0 (136.0)  ,  DataTime: 6.986 (0.039)  ,  ForwardTime: 2.140  ,  TotalTime: 9.165  ,  Loss/total: 0.73654  ,  Loss/giou: 0.17822  ,  Loss/l1: 0.01641  ,  Loss/location: 0.29805  ,  IoU: 0.83552
[train: 1, 50 / 234] FPS: 7.0 (135.9)  ,  DataTime: 7.661 (0.039)  ,  ForwardTime: 1.464  ,  TotalTime: 9.165  ,  Loss/total: 0.69506  ,  Loss/giou: 0.16725  ,  Loss/l1: 0.01444  ,  Loss/location: 0.28833  ,  IoU: 0.84276
[train: 1, 50 / 234] FPS: 7.0 (135.6)  ,  DataTime: 8.290 (0.038)  ,  ForwardTime: 0.837  ,  TotalTime: 9.165  ,  Loss/total: 0.71108  ,  Loss/giou: 0.17143  ,  Loss/l1: 0.01551  ,  Loss/location: 0.29066  ,  IoU: 0.84030
[train: 1, 50 / 234] FPS: 7.0 (135.6)  ,  DataTime: 7.267 (0.038)  ,  ForwardTime: 1.860  ,  TotalTime: 9.165  ,  Loss/total: 0.66838  ,  Loss/giou: 0.16499  ,  Loss/l1: 0.01488  ,  Loss/location: 0.26398  ,  IoU: 0.84627
[Param/Grad/Update Mean] param: 1.566839e-01, grad: 2.481046e-05, update: 3.264465e-05, lr*update: 1.305786e-08
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566839e-01, grad: 2.481046e-05, update: 3.264465e-05, lr*update: 1.305786e-08
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566839e-01, grad: 2.481046e-05, update: 3.264465e-05, lr*update: 1.305786e-08
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566839e-01, grad: 2.481046e-05, update: 3.264465e-05, lr*update: 1.305786e-08
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[train: 1, 100 / 234] FPS: 7.9 (129.9)  ,  DataTime: 6.608 (0.039)  ,  ForwardTime: 1.488  ,  TotalTime: 8.136  ,  Loss/total: 0.70196  ,  Loss/giou: 0.16801  ,  Loss/l1: 0.01448  ,  Loss/location: 0.29357  ,  IoU: 0.84200
[train: 1, 100 / 234] FPS: 7.9 (129.6)  ,  DataTime: 6.499 (0.038)  ,  ForwardTime: 1.599  ,  TotalTime: 8.136  ,  Loss/total: 0.71153  ,  Loss/giou: 0.17201  ,  Loss/l1: 0.01551  ,  Loss/location: 0.28996  ,  IoU: 0.83967
[train: 1, 100 / 234] FPS: 7.9 (129.0)  ,  DataTime: 5.017 (0.037)  ,  ForwardTime: 3.081  ,  TotalTime: 8.136  ,  Loss/total: 0.68844  ,  Loss/giou: 0.16816  ,  Loss/l1: 0.01537  ,  Loss/location: 0.27528  ,  IoU: 0.84409
[train: 1, 100 / 234] FPS: 7.9 (129.7)  ,  DataTime: 5.756 (0.039)  ,  ForwardTime: 2.340  ,  TotalTime: 8.136  ,  Loss/total: 0.72006  ,  Loss/giou: 0.17415  ,  Loss/l1: 0.01559  ,  Loss/location: 0.29381  ,  IoU: 0.83813
[Param/Grad/Update Mean] param: 1.566454e-01, grad: -1.228701e-05, update: -4.454736e-06, lr*update: -1.781894e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566454e-01, grad: -1.228701e-05, update: -4.454736e-06, lr*update: -1.781894e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566454e-01, grad: -1.228701e-05, update: -4.454736e-06, lr*update: -1.781894e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566454e-01, grad: -1.228701e-05, update: -4.454736e-06, lr*update: -1.781894e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[train: 1, 150 / 234] FPS: 8.7 (131.5)  ,  DataTime: 3.513 (0.037)  ,  ForwardTime: 3.838  ,  TotalTime: 7.388  ,  Loss/total: 0.70089  ,  Loss/giou: 0.17069  ,  Loss/l1: 0.01563  ,  Loss/location: 0.28136  ,  IoU: 0.84190
[train: 1, 150 / 234] FPS: 8.7 (131.5)  ,  DataTime: 5.547 (0.039)  ,  ForwardTime: 1.802  ,  TotalTime: 7.388  ,  Loss/total: 0.71922  ,  Loss/giou: 0.17374  ,  Loss/l1: 0.01557  ,  Loss/location: 0.29388  ,  IoU: 0.83839[train: 1, 150 / 234] FPS: 8.7 (131.5)  ,  DataTime: 5.842 (0.040)  ,  ForwardTime: 1.506  ,  TotalTime: 7.388  ,  Loss/total: 0.70131  ,  Loss/giou: 0.16863  ,  Loss/l1: 0.01487  ,  Loss/location: 0.28973  ,  IoU: 0.84245

[train: 1, 150 / 234] FPS: 8.7 (131.2)  ,  DataTime: 5.413 (0.038)  ,  ForwardTime: 1.937  ,  TotalTime: 7.388  ,  Loss/total: 0.71022  ,  Loss/giou: 0.17162  ,  Loss/l1: 0.01565  ,  Loss/location: 0.28872  ,  IoU: 0.84038
[Param/Grad/Update Mean] param: 1.566541e-01, grad: 3.854843e-06, update: 1.168755e-05, lr*update: 4.675019e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566541e-01, grad: 3.854843e-06, update: 1.168755e-05, lr*update: 4.675019e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566541e-01, grad: 3.854843e-06, update: 1.168755e-05, lr*update: 4.675019e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566541e-01, grad: 3.854843e-06, update: 1.168755e-05, lr*update: 4.675019e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[train: 1, 200 / 234] FPS: 9.3 (135.7)  ,  DataTime: 4.923 (0.040)  ,  ForwardTime: 1.929  ,  TotalTime: 6.891  ,  Loss/total: 0.69851  ,  Loss/giou: 0.16861  ,  Loss/l1: 0.01497  ,  Loss/location: 0.28645  ,  IoU: 0.84277
[train: 1, 200 / 234] FPS: 9.3 (136.2)  ,  DataTime: 4.491 (0.038)  ,  ForwardTime: 2.362  ,  TotalTime: 6.891  ,  Loss/total: 0.71686  ,  Loss/giou: 0.17282  ,  Loss/l1: 0.01591  ,  Loss/location: 0.29164  ,  IoU: 0.83978
[train: 1, 200 / 234] FPS: 9.3 (135.4)  ,  DataTime: 5.259 (0.038)  ,  ForwardTime: 1.594  ,  TotalTime: 6.891  ,  Loss/total: 0.70860  ,  Loss/giou: 0.17147  ,  Loss/l1: 0.01536  ,  Loss/location: 0.28887  ,  IoU: 0.84057
[train: 1, 200 / 234] FPS: 9.3 (135.3)  ,  DataTime: 2.663 (0.037)  ,  ForwardTime: 4.191  ,  TotalTime: 6.891  ,  Loss/total: 0.69722  ,  Loss/giou: 0.16924  ,  Loss/l1: 0.01542  ,  Loss/location: 0.28164  ,  IoU: 0.84304
[Param/Grad/Update Mean] param: 1.566547e-01, grad: 9.894216e-06, update: 1.772695e-05, lr*update: 7.090780e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566547e-01, grad: 9.894216e-06, update: 1.772695e-05, lr*update: 7.090780e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566547e-01, grad: 9.894216e-06, update: 1.772695e-05, lr*update: 7.090780e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566547e-01, grad: 9.894216e-06, update: 1.772695e-05, lr*update: 7.090780e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[train: 1, 234 / 234] FPS: 9.9 (133.8)  ,  DataTime: 3.855 (0.038)  ,  ForwardTime: 2.544  ,  TotalTime: 6.438  ,  Loss/total: 0.71491  ,  Loss/giou: 0.17264  ,  Loss/l1: 0.01588  ,  Loss/location: 0.29024  ,  IoU: 0.83992
[train: 1, 234 / 234] FPS: 9.9 (133.8)  ,  DataTime: 4.334 (0.040)  ,  ForwardTime: 2.064  ,  TotalTime: 6.438  ,  Loss/total: 0.69886  ,  Loss/giou: 0.16900  ,  Loss/l1: 0.01500  ,  Loss/location: 0.28586  ,  IoU: 0.84238
[train: 1, 234 / 234] FPS: 9.9 (133.7)  ,  DataTime: 4.927 (0.038)  ,  ForwardTime: 1.472  ,  TotalTime: 6.438  ,  Loss/total: 0.70512  ,  Loss/giou: 0.17069  ,  Loss/l1: 0.01522  ,  Loss/location: 0.28765  ,  IoU: 0.84113
[train: 1, 234 / 234] FPS: 9.9 (133.7)  ,  DataTime: 2.281 (0.037)  ,  ForwardTime: 4.119  ,  TotalTime: 6.438  ,  Loss/total: 0.69928  ,  Loss/giou: 0.16931  ,  Loss/l1: 0.01547  ,  Loss/location: 0.28331  ,  IoU: 0.84312
Epoch Time: 0:25:06.439389
Avg Data Time: 2.28110
Avg GPU Trans Time: 0.03721
Avg Forward Time: 4.11947
Epoch Time: 0:25:06.438118
Avg Data Time: 4.33351
Avg GPU Trans Time: 0.03989
Avg Forward Time: 2.06436
Epoch Time: 0:25:06.452625
Avg Data Time: 3.85537
Avg GPU Trans Time: 0.03823
Avg Forward Time: 2.54423
Epoch Time: 0:25:06.434469
Avg Data Time: 4.92704
Avg GPU Trans Time: 0.03834
Avg Forward Time: 1.47237
[train: 2, 50 / 234] FPS: 13.7 (136.6)  ,  DataTime: 2.783 (0.038)  ,  ForwardTime: 1.854  ,  TotalTime: 4.674  ,  Loss/total: 0.68428  ,  Loss/giou: 0.16354  ,  Loss/l1: 0.01468  ,  Loss/location: 0.28378  ,  IoU: 0.84801
[train: 2, 50 / 234] FPS: 13.7 (137.3)  ,  DataTime: 3.633 (0.042)  ,  ForwardTime: 1.000  ,  TotalTime: 4.675  ,  Loss/total: 0.68233  ,  Loss/giou: 0.16549  ,  Loss/l1: 0.01534  ,  Loss/location: 0.27465  ,  IoU: 0.84700
[train: 2, 50 / 234] FPS: 13.7 (136.7)  ,  DataTime: 3.299 (0.040)  ,  ForwardTime: 1.334  ,  TotalTime: 4.674  ,  Loss/total: 0.70448  ,  Loss/giou: 0.17137  ,  Loss/l1: 0.01550  ,  Loss/location: 0.28421  ,  IoU: 0.84107
[train: 2, 50 / 234] FPS: 13.7 (136.5)  ,  DataTime: 1.525 (0.041)  ,  ForwardTime: 3.109  ,  TotalTime: 4.675  ,  Loss/total: 0.69509  ,  Loss/giou: 0.16918  ,  Loss/l1: 0.01565  ,  Loss/location: 0.27850  ,  IoU: 0.84331
[Param/Grad/Update Mean] param: 1.566281e-01, grad: 4.972285e-05, update: 5.755425e-05, lr*update: 2.300632e-08
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566281e-01, grad: 4.972285e-05, update: 5.755425e-05, lr*update: 2.300632e-08
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566281e-01, grad: 4.972285e-05, update: 5.755425e-05, lr*update: 2.300632e-08
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566281e-01, grad: 4.972285e-05, update: 5.755425e-05, lr*update: 2.300632e-08
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[train: 2, 100 / 234] FPS: 14.9 (12.3)  ,  DataTime: 0.984 (0.041)  ,  ForwardTime: 3.279  ,  TotalTime: 4.303  ,  Loss/total: 0.69023  ,  Loss/giou: 0.16743  ,  Loss/l1: 0.01505  ,  Loss/location: 0.28011  ,  IoU: 0.84421
[train: 2, 100 / 234] FPS: 14.9 (12.3)  ,  DataTime: 2.192 (0.040)  ,  ForwardTime: 2.070  ,  TotalTime: 4.303  ,  Loss/total: 0.70153  ,  Loss/giou: 0.17049  ,  Loss/l1: 0.01540  ,  Loss/location: 0.28355  ,  IoU: 0.84156
[train: 2, 100 / 234] FPS: 14.9 (12.3)  ,  DataTime: 3.307 (0.041)  ,  ForwardTime: 0.956  ,  TotalTime: 4.304  ,  Loss/total: 0.69330  ,  Loss/giou: 0.16835  ,  Loss/l1: 0.01553  ,  Loss/location: 0.27893  ,  IoU: 0.84413
[train: 2, 100 / 234] FPS: 14.9 (12.3)  ,  DataTime: 1.960 (0.038)  ,  ForwardTime: 2.305  ,  TotalTime: 4.303  ,  Loss/total: 0.68172  ,  Loss/giou: 0.16427  ,  Loss/l1: 0.01478  ,  Loss/location: 0.27929  ,  IoU: 0.84720
[Param/Grad/Update Mean] param: 1.566284e-01, grad: -1.900047e-05, update: -1.116905e-05, lr*update: -4.464636e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566284e-01, grad: -1.900047e-05, update: -1.116905e-05, lr*update: -4.464636e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566284e-01, grad: -1.900047e-05, update: -1.116905e-05, lr*update: -4.464636e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566284e-01, grad: -1.900047e-05, update: -1.116905e-05, lr*update: -4.464636e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[train: 2, 150 / 234] FPS: 15.9 (12.4)  ,  DataTime: 1.418 (0.041)  ,  ForwardTime: 2.565  ,  TotalTime: 4.024  ,  Loss/total: 0.69705  ,  Loss/giou: 0.16974  ,  Loss/l1: 0.01540  ,  Loss/location: 0.28056  ,  IoU: 0.84235
[train: 2, 150 / 234] FPS: 15.9 (12.5)  ,  DataTime: 2.378 (0.040)  ,  ForwardTime: 1.605  ,  TotalTime: 4.024  ,  Loss/total: 0.69788  ,  Loss/giou: 0.16982  ,  Loss/l1: 0.01573  ,  Loss/location: 0.27960  ,  IoU: 0.84303
[train: 2, 150 / 234] FPS: 15.9 (12.4)  ,  DataTime: 1.622 (0.041)  ,  ForwardTime: 2.361  ,  TotalTime: 4.023  ,  Loss/total: 0.69510  ,  Loss/giou: 0.16837  ,  Loss/l1: 0.01501  ,  Loss/location: 0.28332  ,  IoU: 0.84320
[train: 2, 150 / 234] FPS: 15.9 (12.5)  ,  DataTime: 1.607 (0.038)  ,  ForwardTime: 2.378  ,  TotalTime: 4.024  ,  Loss/total: 0.69821  ,  Loss/giou: 0.16869  ,  Loss/l1: 0.01538  ,  Loss/location: 0.28394  ,  IoU: 0.84352
[Param/Grad/Update Mean] param: 1.567637e-01, grad: 1.883186e-06, update: 9.721371e-06, lr*update: 3.885951e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.567637e-01, grad: 1.883186e-06, update: 9.721371e-06, lr*update: 3.885951e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.567637e-01, grad: 1.883186e-06, update: 9.721371e-06, lr*update: 3.885951e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.567637e-01, grad: 1.883186e-06, update: 9.721371e-06, lr*update: 3.885951e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[train: 2, 200 / 234] FPS: 16.9 (15.2)  ,  DataTime: 1.226 (0.041)  ,  ForwardTime: 2.528  ,  TotalTime: 3.795  ,  Loss/total: 0.69139  ,  Loss/giou: 0.16763  ,  Loss/l1: 0.01495  ,  Loss/location: 0.28138  ,  IoU: 0.84386
[train: 2, 200 / 234] FPS: 16.9 (15.2)  ,  DataTime: 1.272 (0.039)  ,  ForwardTime: 2.485  ,  TotalTime: 3.796  ,  Loss/total: 0.69427  ,  Loss/giou: 0.16838  ,  Loss/l1: 0.01523  ,  Loss/location: 0.28139  ,  IoU: 0.84356
[train: 2, 200 / 234] FPS: 16.9 (15.2)  ,  DataTime: 1.724 (0.041)  ,  ForwardTime: 2.031  ,  TotalTime: 3.796  ,  Loss/total: 0.70008  ,  Loss/giou: 0.17111  ,  Loss/l1: 0.01577  ,  Loss/location: 0.27901  ,  IoU: 0.84170
[train: 2, 200 / 234] FPS: 16.9 (15.2)  ,  DataTime: 1.793 (0.041)  ,  ForwardTime: 1.962  ,  TotalTime: 3.796  ,  Loss/total: 0.69696  ,  Loss/giou: 0.16970  ,  Loss/l1: 0.01549  ,  Loss/location: 0.28008  ,  IoU: 0.84267
[Param/Grad/Update Mean] param: 1.566724e-01, grad: 2.014135e-07, update: 8.035031e-06, lr*update: 3.211865e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566724e-01, grad: 2.014135e-07, update: 8.035031e-06, lr*update: 3.211865e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566724e-01, grad: 2.014135e-07, update: 8.035031e-06, lr*update: 3.211865e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[Param/Grad/Update Mean] param: 1.566724e-01, grad: 2.014135e-07, update: 8.035031e-06, lr*update: 3.211865e-09
[Param Count] total: 360, trainable: 44, voxel: 44, with_grad: 44
[train: 2, 234 / 234] FPS: 18.1 (136.8)  ,  DataTime: 1.053 (0.041)  ,  ForwardTime: 2.441  ,  TotalTime: 3.535  ,  Loss/total: 0.69372  ,  Loss/giou: 0.16816  ,  Loss/l1: 0.01497  ,  Loss/location: 0.28256  ,  IoU: 0.84332
[train: 2, 234 / 234] FPS: 18.1 (137.1)  ,  DataTime: 1.537 (0.040)  ,  ForwardTime: 1.958  ,  TotalTime: 3.535  ,  Loss/total: 0.69517  ,  Loss/giou: 0.16886  ,  Loss/l1: 0.01536  ,  Loss/location: 0.28067  ,  IoU: 0.84322
[train: 2, 234 / 234] FPS: 18.1 (136.8)  ,  DataTime: 1.092 (0.039)  ,  ForwardTime: 2.404  ,  TotalTime: 3.535  ,  Loss/total: 0.69408  ,  Loss/giou: 0.16790  ,  Loss/l1: 0.01513  ,  Loss/location: 0.28262  ,  IoU: 0.84380
[train: 2, 234 / 234] FPS: 18.1 (136.4)  ,  DataTime: 1.698 (0.041)  ,  ForwardTime: 1.797  ,  TotalTime: 3.535  ,  Loss/total: 0.69928  ,  Loss/giou: 0.17080  ,  Loss/l1: 0.01568  ,  Loss/location: 0.27927  ,  IoU: 0.84184
Epoch Time: 0:13:47.300487
Avg Data Time: 1.53686
Avg GPU Trans Time: 0.04040
Avg Forward Time: 1.95821
Epoch Time: 0:13:47.293456
Avg Data Time: 1.69752
Avg GPU Trans Time: 0.04127
Avg Forward Time: 1.79665
Epoch Time: 0:13:47.236871
Avg Data Time: 1.05343
Avg GPU Trans Time: 0.04107
Avg Forward Time: 2.44070
Epoch Time: 0:13:47.264453
Avg Data Time: 1.09229
Avg GPU Trans Time: 0.03945
Avg Forward Time: 2.40358
