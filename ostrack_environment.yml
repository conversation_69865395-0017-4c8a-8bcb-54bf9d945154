name: ostrack
channels:
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_gnu
  - bzip2=1.0.8=h4bc722e_7
  - ca-certificates=2024.12.14=hbcca054_0
  - ld_impl_linux-64=2.43=h712a8e2_2
  - libffi=3.4.2=h7f98852_5
  - libgcc=14.2.0=h77fa898_1
  - libgcc-ng=14.2.0=h69a702a_1
  - libgomp=14.2.0=h77fa898_1
  - liblzma=5.6.3=hb9d3cd8_1
  - libnsl=2.0.1=hd590300_0
  - libsqlite=3.47.2=hee588c1_0
  - libuuid=2.38.1=h0b41bf4_0
  - libxcrypt=4.4.36=hd590300_1
  - libzlib=1.3.1=hb9d3cd8_2
  - ncurses=6.5=h2d0b736_2
  - openssl=3.4.0=h7b32b05_1
  - pip=24.3.1=pyh8b19718_2
  - python=3.9.21=h9c0c6dc_1_cpython
  - readline=8.2=h8228510_1
  - setuptools=75.8.0=pyhff2d567_0
  - tk=8.6.13=noxft_h4845f30_101
  - wheel=0.45.1=pyhd8ed1ab_1
  - pip:
      - absl-py==2.1.0
      - addict==2.4.0
      - annotated-types==0.7.0
      - asttokens==3.0.0
      - ccimport==0.4.4
      - certifi==2024.12.14
      - cffi==1.17.1
      - charset-normalizer==3.4.1
      - click==8.1.8
      - colorama==0.4.6
      - comm==0.2.2
      - contourpy==1.3.0
      - cumm==0.7.11
      - cumm-cu120==0.4.11
      - cycler==0.12.1
      - cython==3.0.12
      - debugpy==1.8.11
      - decorator==5.1.1
      - deprecated==1.2.15
      - docker-pycreds==0.4.0
      - dv==1.0.12
      - dv-processing==1.7.9
      - easydict==1.13
      - eval-type-backport==0.2.2
      - exceptiongroup==1.2.2
      - executing==2.1.0
      - filelock==3.16.1
      - fire==0.7.0
      - flatbuffers==24.12.23
      - fonttools==4.55.3
      - fsspec==2024.12.0
      - future==1.0.0
      - gitdb==4.0.12
      - gitpython==3.1.44
      - grpcio==1.70.0
      - huggingface-hub==0.28.1
      - idna==3.10
      - imageio==2.37.0
      - importlib-metadata==8.5.0
      - importlib-resources==6.5.2
      - ipykernel==6.29.5
      - ipython==8.18.1
      - jedi==0.19.2
      - jinja2==3.1.5
      - jpeg4py==0.1.4
      - jsonpatch==1.33
      - jsonpointer==3.0.0
      - jupyter-client==8.6.3
      - jupyter-core==5.7.2
      - kiwisolver==1.4.7
      - lark==1.2.2
      - lazy-loader==0.4
      - lmdb==1.6.2
      - lz4==4.3.3
      - markdown==3.7
      - markupsafe==3.0.2
      - matplotlib==3.9.4
      - matplotlib-inline==0.1.7
      - mpmath==1.3.0
      - nest-asyncio==1.6.0
      - networkx==3.2.1
      - ninja==********
      - numpy==1.26.4
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.4.127
      - nvidia-cuda-nvrtc-cu12==12.4.127
      - nvidia-cuda-runtime-cu12==12.4.127
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==********
      - nvidia-cusparse-cu12==**********
      - nvidia-ml-py==12.570.86
      - nvidia-nccl-cu12==2.21.5
      - nvidia-nvjitlink-cu12==12.4.127
      - nvidia-nvtx-cu12==12.4.127
      - nvitop==1.4.2
      - opencv-python==4.10.0.84
      - packaging==24.2
      - pandas==2.2.3
      - parso==0.8.4
      - pccm==0.4.16
      - pexpect==4.9.0
      - pillow==11.1.0
      - platformdirs==4.3.6
      - portalocker==3.1.1
      - prompt-toolkit==3.0.48
      - protobuf==5.29.3
      - psutil==6.1.1
      - ptyprocess==0.7.0
      - pure-eval==0.2.3
      - pybind11==2.13.6
      - pycocotools==2.0.8
      - pycparser==2.22
      - pydantic==2.10.6
      - pydantic-core==2.27.2
      - pygments==2.19.1
      - pyparsing==3.2.1
      - python-dateutil==2.9.0.post0
      - pytz==2024.2
      - pyyaml==6.0.2
      - pyzmq==26.2.0
      - requests==2.32.3
      - safetensors==0.5.2
      - scikit-image==0.24.0
      - scipy==1.13.1
      - sentry-sdk==2.21.0
      - setproctitle==1.3.4
      - setuptools-scm==8.2.0
      - six==1.17.0
      - smmap==5.0.2
      - spconv-cu120==2.3.6
      - stack-data==0.6.3
      - sympy==1.13.1
      - tb-nightly==2.19.0a20250217
      - tensorboard-data-server==0.7.2
      - tensorboardx==*******
      - termcolor==2.5.0
      - thop==0.1.1-2209072238
      - tifffile==2024.8.30
      - tikzplotlib==0.10.1
      - timm==1.0.14
      - tomli==2.2.1
      - torch==2.5.1
      - torchaudio==2.5.1
      - torchvision==0.20.1
      - tornado==6.4.2
      - tqdm==4.67.1
      - traitlets==5.14.3
      - triton==3.1.0
      - typing-extensions==4.12.2
      - tzdata==2024.2
      - urllib3==2.3.0
      - visdom==0.2.4
      - wandb==0.19.6
      - wcwidth==0.2.13
      - webcolors==24.11.1
      - websocket-client==1.8.0
      - werkzeug==3.1.3
      - wrapt==1.17.1
      - yapf==0.43.0
      - zipp==3.21.0
      - zstd==*******
prefix: /home/<USER>/anaconda3/envs/ostrack
