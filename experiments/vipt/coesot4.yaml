DATA:
  MAX_SAMPLE_INTERVAL: 200
  MEAN:
  - 0.485
  - 0.456
  - 0.406
  SEARCH:
    CENTER_JITTER: 3
    FACTOR: 4.0
    SCALE_JITTER: 0.25
    SIZE: 256
    NUMBER: 1
  STD:
  - 0.229
  - 0.224
  - 0.225
  TEMPLATE:
    CENTER_JITTER: 0
    FACTOR: 2.0
    SCALE_JITTER: 0
    SIZE: 128
  TRAIN:
    DATASETS_NAME:
    - COESOT
    DATASETS_RATIO:
    - 1
    SAMPLE_PER_EPOCH: 60000
  VAL:
    DATASETS_NAME:
    - COESOT_Val
    DATASETS_RATIO:
    - 1
    SAMPLE_PER_EPOCH: 40000
MODEL:
  PRETRAIN_FILE: "./pretrained/OSTrack_coesot.pth.tar"
  EXTRA_MERGER: False
  RETURN_INTER: False
  BACKBONE:
    TYPE: vit_base_patch16_224_ce_prompt
    STRIDE: 16
    CE_LOC: [3, 6, 9]
    CE_KEEP_RATIO: [0.7, 0.7, 0.7]
    CE_TEMPLATE_RANGE: 'CTR_POINT'  # choose between ALL, CTR_POINT, CTR_REC, GT_BOX
  HEAD:
    TYPE: CENTER
    NUM_CHANNELS: 256
TRAIN:
  BACKBONE_MULTIPLIER: 0.1
  DROP_PATH_RATE: 0.1
  CE_START_EPOCH: 4  # candidate elimination start epoch 1/15
  CE_WARM_EPOCH: 16  # candidate elimination warm up epoch 4/15
  BATCH_SIZE: 64  # 32
  EPOCH: 60
  GIOU_WEIGHT: 2.0
  L1_WEIGHT: 5.0
  LOCATION_WEIGHT: 1.0
  GRAD_CLIP_NORM: 0.1
  LR: 0.0004
  # LR_DROP_EPOCH: 48  # 4/5
  NUM_WORKER: 10
  OPTIMIZER: ADAMW
  PRINT_INTERVAL: 50
  SCHEDULER:
    TYPE: "cosine"
    T_MAX: 60  # 假设训练300个epochs
    ETA_MIN: 0.00001  # 最小学习率
  VAL_EPOCH_INTERVAL: 5
  WEIGHT_DECAY: 0.00005
  AMP: False
  PROMPT:
    TYPE: vipt_deep
  FIX_BN: True
  SAVE_EPOCH_INTERVAL: 5
  SAVE_LAST_N_EPOCH: 1
TEST:
  EPOCH: 60
  SEARCH_FACTOR: 4.0
  SEARCH_SIZE: 256
  TEMPLATE_FACTOR: 2.0
  TEMPLATE_SIZE: 128
