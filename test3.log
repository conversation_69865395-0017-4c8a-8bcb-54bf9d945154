nohup: 忽略输入
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:47: FutureWarning: `torch.cuda.amp.custom_fwd(args...)` is deprecated. Please use `torch.amp.custom_fwd(args..., device_type='cuda')` instead.
  _TORCH_CUSTOM_FWD = amp.custom_fwd(cast_inputs=torch.float16)
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:97: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:163: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:243: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:332: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:369: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:389: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
/home/<USER>/anaconda3/envs/ostrack/lib/python3.9/site-packages/spconv/pytorch/functional.py:412: FutureWarning: `torch.cuda.amp.custom_bwd(args...)` is deprecated. Please use `torch.amp.custom_bwd(args..., device_type='cuda')` instead.
  def backward(ctx, grad_output):
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00471_UAV_outdoor6——————————————
00471_UAV_outdoor6 , fps:5.0585205803287145
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00473_UAV_outdoor6——————————————
00473_UAV_outdoor6 , fps:4.112682650913136
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00478_UAV_outdoor6——————————————
00478_UAV_outdoor6 , fps:4.502776513310579
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00490_UAV_outdoor6——————————————
00490_UAV_outdoor6 , fps:3.6817495602272663
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00435_UAV_outdoor6——————————————
00435_UAV_outdoor6 , fps:4.279926393886186
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00437_UAV_outdoor6——————————————
00437_UAV_outdoor6 , fps:3.6251237712878455
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00439_UAV_outdoor6——————————————
00439_UAV_outdoor6 , fps:2.0734335434058204
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00442_UAV_outdoor6——————————————
00442_UAV_outdoor6 , fps:1.1226407483685032
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00508_person_outdoor6——————————————
00508_person_outdoor6 , fps:0.9684902462925729
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00510_person_outdoor6——————————————
00510_person_outdoor6 , fps:1.1196032279728914
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00511_person_outdoor6——————————————
00511_person_outdoor6 , fps:3.121689023659819
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00514_person_outdoor6——————————————
00514_person_outdoor6 , fps:0.9070934648703299
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00421_UAV_outdoor6——————————————
00421_UAV_outdoor6 , fps:0.8587766890730681
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00423_UAV_outdoor6——————————————
00423_UAV_outdoor6 , fps:2.389654352795311
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00425_UAV_outdoor6——————————————
00425_UAV_outdoor6 , fps:0.8352332462148093
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00430_UAV_outdoor6——————————————
00430_UAV_outdoor6 , fps:0.9661817716403597
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00449_UAV_outdoor6——————————————
00449_UAV_outdoor6 , fps:1.0310004217572204
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00451_UAV_outdoor6——————————————
00451_UAV_outdoor6 , fps:0.9732302977036029
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00453_UAV_outdoor6——————————————
00453_UAV_outdoor6 , fps:0.924992991066113
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00458_UAV_outdoor6——————————————
00458_UAV_outdoor6 , fps:1.0237513508802838
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00345_UAV_outdoor6——————————————
00345_UAV_outdoor6 , fps:1.0076388507346374
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00351_UAV_outdoor6——————————————
00351_UAV_outdoor6 , fps:1.5958766424914586
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00355_UAV_outdoor6——————————————
00355_UAV_outdoor6 , fps:0.8610545257934147
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00370_UAV_outdoor6——————————————
00370_UAV_outdoor6 , fps:0.9083740099286524
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00404_UAV_outdoor6——————————————
00404_UAV_outdoor6 , fps:0.8979294121732682
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00406_UAV_outdoor6——————————————
00406_UAV_outdoor6 , fps:1.0161830261046234
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00408_UAV_outdoor6——————————————
00408_UAV_outdoor6 , fps:1.1107546049539925
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00410_UAV_outdoor6——————————————
00410_UAV_outdoor6 , fps:0.9685317805306966
test config:  test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00141_tank_outdoor2——————————————
00141_tank_outdoor2 , fps:0.7867768900358773
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00147_tank_outdoor2——————————————
00147_tank_outdoor2 , fps:0.9041854316530893
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00197_driving_outdoor3——————————————
00197_driving_outdoor3 , fps:0.8598114940842643
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00236_tennis_outdoor4——————————————
00236_tennis_outdoor4 , fps:1.7865567630904609
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00445_UAV_outdoor6——————————————
00445_UAV_outdoor6 , fps:1.0815045544729787
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00447_UAV_outdoor6——————————————
00447_UAV_outdoor6 , fps:3.0734660669656417
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_08_56_18_windowPattern——————————————
dvSave-2021_02_06_08_56_18_windowPattern , fps:0.9730241897435715
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_08_56_40_windowPattern2——————————————
dvSave-2021_02_06_08_56_40_windowPattern2 , fps:1.382994683218592
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00241_tennis_outdoor4——————————————
00241_tennis_outdoor4 , fps:1.1523637104609892
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00340_UAV_outdoor6——————————————
00340_UAV_outdoor6 , fps:1.2972106335104283
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_15_15_36_redcar——————————————
dvSave-2021_02_06_15_15_36_redcar , fps:1.1875234138230093
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_15_17_48_whitecar——————————————
dvSave-2021_02_06_15_17_48_whitecar , fps:1.2930149056275624
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00432_UAV_outdoor6——————————————
00432_UAV_outdoor6 , fps:1.2871220348265802
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00433_UAV_outdoor6——————————————
00433_UAV_outdoor6 , fps:1.087654039648542
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_09_36_15_Pedestrian——————————————
dvSave-2021_02_06_09_36_15_Pedestrian , fps:1.1378033147603877
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_09_36_44_Pedestrian——————————————
dvSave-2021_02_06_09_36_44_Pedestrian , fps:1.2027449235661156
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00413_UAV_outdoor6——————————————
00413_UAV_outdoor6 , fps:0.9901545492939234
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00416_UAV_outdoor6——————————————
00416_UAV_outdoor6 , fps:1.0226888247567534
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_36_49_personBasketball——————————————
dvSave-2021_02_06_17_36_49_personBasketball , fps:1.633710273783534
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_41_45_personBasketball——————————————
dvSave-2021_02_06_17_41_45_personBasketball , fps:3.866586058285334
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00503_UAV_outdoor6——————————————
00503_UAV_outdoor6 , fps:2.4861248898391635
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00506_person_outdoor6——————————————
00506_person_outdoor6 , fps:3.564931059298384
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_04_20_41_53——————————————
dvSave-2021_02_04_20_41_53 , fps:1.0981279033701656
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_04_20_49_43——————————————
dvSave-2021_02_04_20_49_43 , fps:2.571721380822094
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00385_UAV_outdoor6——————————————
00385_UAV_outdoor6 , fps:1.0232158849712902
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00398_UAV_outdoor6——————————————
00398_UAV_outdoor6 , fps:1.0739186057562173
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_21_41_personFootball——————————————
dvSave-2021_02_06_17_21_41_personFootball , fps:1.6217222276804517
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_23_26_personFootball——————————————
dvSave-2021_02_06_17_23_26_personFootball , fps:1.2548748405081067
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_09_16_35_car——————————————
dvSave-2021_02_06_09_16_35_car , fps:1.1996473090981068
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_09_21_53_car——————————————
dvSave-2021_02_06_09_21_53_car , fps:1.4783006001064802
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_09_24_26_Pedestrian1——————————————
dvSave-2021_02_06_09_24_26_Pedestrian1 , fps:1.43123252125753
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_09_35_08_Pedestrian——————————————
dvSave-2021_02_06_09_35_08_Pedestrian , fps:1.9037995616743009
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00464_UAV_outdoor6——————————————
00464_UAV_outdoor6 , fps:0.9105824365465889
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: 00466_UAV_outdoor6——————————————
00466_UAV_outdoor6 , fps:1.2918507124250826
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_10_11_59_paperClips——————————————
dvSave-2021_02_06_10_11_59_paperClips , fps:1.3609234681266142
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_10_14_17_paperClip——————————————
dvSave-2021_02_06_10_14_17_paperClip , fps:1.7128612513421277
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_04_20_56_55——————————————
dvSave-2021_02_04_20_56_55 , fps:1.2854208267710538
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_04_21_18_52——————————————
dvSave-2021_02_04_21_18_52 , fps:1.8781001368687156
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_04_21_20_22——————————————
dvSave-2021_02_04_21_20_22 , fps:2.113594285444008
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_04_21_21_24——————————————
dvSave-2021_02_04_21_21_24 , fps:2.538375945604235
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_15_18_36_redcar——————————————
dvSave-2021_02_06_15_18_36_redcar , fps:1.2970021310811481
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_15_20_whitecar——————————————
dvSave-2021_02_06_17_15_20_whitecar , fps:1.3313891057184561
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_16_26_whitecar——————————————
dvSave-2021_02_06_17_16_26_whitecar , fps:1.8680372009767088
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_20_28_personFootball——————————————
dvSave-2021_02_06_17_20_28_personFootball , fps:2.1366556324212587
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: UAV_long_001——————————————
UAV_long_001 , fps:1.3523029345845738
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dightNUM_001——————————————
dightNUM_001 , fps:1.7154781747264607
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_53_39_personFootball——————————————
dvSave-2021_02_06_17_53_39_personFootball , fps:2.283690605441154
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_57_54_personFootball——————————————
dvSave-2021_02_06_17_57_54_personFootball , fps:6.26017884713248
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_45_17_personBasketball——————————————
dvSave-2021_02_06_17_45_17_personBasketball , fps:3.820285218705502
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_47_49_personBasketball——————————————
dvSave-2021_02_06_17_47_49_personBasketball , fps:2.009902387956929
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_49_51_personBasketball——————————————
dvSave-2021_02_06_17_49_51_personBasketball , fps:1.9516854094550544
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_51_05_personBasketball——————————————
dvSave-2021_02_06_17_51_05_personBasketball , fps:3.9086810737908078
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_27_53_personFootball——————————————
dvSave-2021_02_06_17_27_53_personFootball , fps:1.8836009255691772
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_31_03_personBasketball——————————————
dvSave-2021_02_06_17_31_03_personBasketball , fps:2.0162439902114495
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_33_01_personBasketball——————————————
dvSave-2021_02_06_17_33_01_personBasketball , fps:3.1359933677961207
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_17_34_58_personBasketball——————————————
dvSave-2021_02_06_17_34_58_personBasketball , fps:2.91267997270526
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_14_16_56_01_house——————————————
dvSave-2021_02_14_16_56_01_house , fps:2.5564184767454092
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_14_17_00_48——————————————
dvSave-2021_02_14_17_00_48 , fps:5.458867674458609
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_10_12_19_basketball——————————————
dvSave-2021_02_15_10_12_19_basketball , fps:5.998400344641556
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_10_14_18_chicken——————————————
dvSave-2021_02_15_10_14_18_chicken , fps:7.939302051649999
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_13_01_16_Duck——————————————
dvSave-2021_02_15_13_01_16_Duck , fps:2.542440632267023
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_13_02_21_Chicken——————————————
dvSave-2021_02_15_13_02_21_Chicken , fps:4.33707942903221
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_13_04_57_Duck——————————————
dvSave-2021_02_15_13_04_57_Duck , fps:3.9720051721315426
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_13_05_43_Chicken——————————————
dvSave-2021_02_15_13_05_43_Chicken , fps:4.537589905890093
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_10_23_05_basketall——————————————
dvSave-2021_02_15_10_23_05_basketall , fps:3.7834488493780323
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_10_23_05_boyhead——————————————
dvSave-2021_02_15_10_23_05_boyhead , fps:7.053024592442193
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_10_26_11_chicken——————————————
dvSave-2021_02_15_10_26_11_chicken , fps:4.030904502477534
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_12_44_27_chicken——————————————
dvSave-2021_02_15_12_44_27_chicken , fps:2.8385179995802785
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_10_17_16_paperClips——————————————
dvSave-2021_02_06_10_17_16_paperClips , fps:3.8199110994323413
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_15_08_41_flag——————————————
dvSave-2021_02_06_15_08_41_flag , fps:2.9284392379346404
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_15_12_44_car——————————————
dvSave-2021_02_06_15_12_44_car , fps:1.8712813242022992
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_15_14_26_blackcar——————————————
dvSave-2021_02_06_15_14_26_blackcar , fps:2.7303215661455624
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_13_10_54_person——————————————
dvSave-2021_02_15_13_10_54_person , fps:2.5486922568736663
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_13_12_45_redcar——————————————
dvSave-2021_02_15_13_12_45_redcar , fps:2.2690122114808693
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_13_13_44_whitecar——————————————
dvSave-2021_02_15_13_13_44_whitecar , fps:2.132607008083292
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_13_14_18_blackcar——————————————
dvSave-2021_02_15_13_14_18_blackcar , fps:2.205779082952162
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_12_13_43_54——————————————
dvSave-2021_02_12_13_43_54 , fps:2.4408787423914577
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_12_13_46_18——————————————
dvSave-2021_02_12_13_46_18 , fps:3.50443820139183
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_12_13_56_29——————————————
dvSave-2021_02_12_13_56_29 , fps:3.473303044154738
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_14_16_21_40——————————————
dvSave-2021_02_14_16_21_40 , fps:2.867157768104455
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_09_58_27_DigitAI——————————————
dvSave-2021_02_06_09_58_27_DigitAI , fps:1.4374731815890143
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_10_03_17_GreenPlant——————————————
dvSave-2021_02_06_10_03_17_GreenPlant , fps:2.5148651382583433
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_10_05_38_phone——————————————
dvSave-2021_02_06_10_05_38_phone , fps:3.514207974869402
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_06_10_09_04_bottle——————————————
dvSave-2021_02_06_10_09_04_bottle , fps:4.599005464005125
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_10_22_23_basketball——————————————
dvSave-2021_02_15_10_22_23_basketball , fps:5.055374915456067
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_10_22_23_boyhead——————————————
dvSave-2021_02_15_10_22_23_boyhead , fps:4.800190895132358
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_16_17_12_18——————————————
dvSave-2021_02_16_17_12_18 , fps:7.683995490748171
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_16_17_15_53——————————————
dvSave-2021_02_16_17_15_53 , fps:8.368611486007964
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_08_21_05_56_motor——————————————
dvSave-2021_02_08_21_05_56_motor , fps:3.055382103635325
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_08_21_07_52——————————————
dvSave-2021_02_08_21_07_52 , fps:2.31862783567195
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_12_13_38_26——————————————
dvSave-2021_02_12_13_38_26 , fps:3.589593020556983
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_12_13_39_56——————————————
dvSave-2021_02_12_13_39_56 , fps:3.4831164330005526
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_13_08_12_blackcar——————————————
dvSave-2021_02_15_13_08_12_blackcar , fps:2.3557522778997875
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_13_09_09_person——————————————
dvSave-2021_02_15_13_09_09_person , fps:3.65531035781401
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0005——————————————
video_0005 , fps:3.367098270845806
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0008——————————————
video_0008 , fps:2.9923400801358873
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_13_27_20_bottle——————————————
dvSave-2021_02_15_13_27_20_bottle , fps:4.149496034336155
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_13_28_20_cash——————————————
dvSave-2021_02_15_13_28_20_cash , fps:3.6097771995759613
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_23_51_36——————————————
dvSave-2021_02_15_23_51_36 , fps:2.7316690035177755
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_23_54_17——————————————
dvSave-2021_02_15_23_54_17 , fps:3.325375005279267
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_13_24_03_girlhead——————————————
dvSave-2021_02_15_13_24_03_girlhead , fps:2.80685506717609
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_13_24_49_girlhead——————————————
dvSave-2021_02_15_13_24_49_girlhead , fps:4.02690854609334
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: tennis_long_003——————————————
tennis_long_003 , fps:3.687629481041364
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: tennis_long_004——————————————
tennis_long_004 , fps:3.0295729790506676
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_16_17_38_25——————————————
dvSave-2021_02_16_17_38_25 , fps:4.47821226001415
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_16_17_42_50——————————————
dvSave-2021_02_16_17_42_50 , fps:4.273149545288265
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dydrant_001——————————————
dydrant_001 , fps:2.8282085451264596
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: roadLight_001——————————————
roadLight_001 , fps:2.424687731240174
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_14_16_22_06——————————————
dvSave-2021_02_14_16_22_06 , fps:2.4600493543802564
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_14_16_53_15_flag——————————————
dvSave-2021_02_14_16_53_15_flag , fps:3.4896654258558333
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0070——————————————
video_0070 , fps:2.578292354002405
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0073——————————————
video_0073 , fps:3.0368400308122094
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_12_45_02_Duck——————————————
dvSave-2021_02_15_12_45_02_Duck , fps:2.281096638185828
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_12_53_54_personHead——————————————
dvSave-2021_02_15_12_53_54_personHead , fps:2.491947577007795
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0029——————————————
video_0029 , fps:3.278737874010874
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0032——————————————
video_0032 , fps:6.368972511271994
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0054——————————————
video_0054 , fps:6.307539946172589
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0056——————————————
video_0056 , fps:6.296588367394308
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0058——————————————
video_0058 , fps:3.614202007320224
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0060——————————————
video_0060 , fps:5.632466648835612
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0076——————————————
video_0076 , fps:2.702396565669598
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0079——————————————
video_0079 , fps:7.005938373665043
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_16_17_20_20——————————————
dvSave-2021_02_16_17_20_20 , fps:8.133863250786733
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_16_17_23_10——————————————
dvSave-2021_02_16_17_23_10 , fps:7.088951130833439
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_16_17_29_37——————————————
dvSave-2021_02_16_17_29_37 , fps:6.671225754268686
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_16_17_34_11——————————————
dvSave-2021_02_16_17_34_11 , fps:7.738024987790735
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_15_23_56_17——————————————
dvSave-2021_02_15_23_56_17 , fps:3.1970990100230425
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: dvSave-2021_02_16_17_07_38——————————————
dvSave-2021_02_16_17_07_38 , fps:2.8282200541365694
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: tennis_long_001——————————————
tennis_long_001 , fps:3.36652094381696
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: tennis_long_002——————————————
tennis_long_002 , fps:3.8830868471936806
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0015——————————————
video_0015 , fps:3.470127025379282
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0018——————————————
video_0018 , fps:2.7502767345275294
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0021——————————————
video_0021 , fps:3.1999074973420734
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0026——————————————
video_0026 , fps:4.117474976966399
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0039——————————————
video_0039 , fps:8.03577905063139
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0045——————————————
video_0045 , fps:6.29919907719415
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0049——————————————
video_0049 , fps:7.629019825103971
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0050——————————————
video_0050 , fps:9.419205544748671
{'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0064——————————————
video_0064 , fps:7.040462809657082
test config:  {'MODEL': {'PRETRAIN_FILE': './pretrained/OSTrack_visevent.pth.tar', 'EXTRA_MERGER': False, 'RETURN_INTER': False, 'RETURN_STAGES': [], 'BACKBONE': {'TYPE': 'vit_base_patch16_224_ce_prompt', 'STRIDE': 16, 'MID_PE': False, 'SEP_SEG': False, 'CAT_MODE': 'direct', 'MERGE_LAYER': 0, 'ADD_CLS_TOKEN': False, 'CLS_TOKEN_USE_MODE': 'ignore', 'CE_LOC': [3, 6, 9], 'CE_KEEP_RATIO': [0.7, 0.7, 0.7], 'CE_TEMPLATE_RANGE': 'CTR_POINT'}, 'HEAD': {'TYPE': 'CENTER', 'NUM_CHANNELS': 256}}, 'TRAIN': {'PROMPT': {'TYPE': 'vipt_deep'}, 'LR': 0.0001, 'WEIGHT_DECAY': 0.0001, 'EPOCH': 60, 'LR_DROP_EPOCH': 400, 'BATCH_SIZE': 64, 'NUM_WORKER': 10, 'OPTIMIZER': 'ADAMW', 'BACKBONE_MULTIPLIER': 0.1, 'GIOU_WEIGHT': 2.0, 'L1_WEIGHT': 5.0, 'LOCATION_WEIGHT': 1.0, 'FREEZE_LAYERS': [0], 'PRINT_INTERVAL': 50, 'VAL_EPOCH_INTERVAL': 1, 'GRAD_CLIP_NORM': 0.1, 'AMP': False, 'FIX_BN': True, 'SAVE_EPOCH_INTERVAL': 5, 'SAVE_LAST_N_EPOCH': 1, 'CE_START_EPOCH': 4, 'CE_WARM_EPOCH': 16, 'DROP_PATH_RATE': 0.1, 'SCHEDULER': {'TYPE': 'cosine', 'DECAY_RATE': 0.1, 'T_MAX': 60, 'ETA_MIN': 0.0001}}, 'DATA': {'SAMPLER_MODE': 'causal', 'MEAN': [0.485, 0.456, 0.406], 'STD': [0.229, 0.224, 0.225], 'MAX_SAMPLE_INTERVAL': 200, 'TRAIN': {'DATASETS_NAME': ['VisEvent'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 60000}, 'VAL': {'DATASETS_NAME': ['VisEvent_Val'], 'DATASETS_RATIO': [1], 'SAMPLE_PER_EPOCH': 20000}, 'SEARCH': {'SIZE': 256, 'FACTOR': 4.0, 'CENTER_JITTER': 3, 'SCALE_JITTER': 0.25, 'NUMBER': 1}, 'TEMPLATE': {'NUMBER': 1, 'SIZE': 128, 'FACTOR': 2.0, 'CENTER_JITTER': 0, 'SCALE_JITTER': 0}}, 'TEST': {'TEMPLATE_FACTOR': 2.0, 'TEMPLATE_SIZE': 128, 'SEARCH_FACTOR': 4.0, 'SEARCH_SIZE': 256, 'EPOCH': 55}}
——————————Process sequence: video_0067——————————————
video_0067 , fps:6.435462323199863
Totally cost 7459.72722196579 seconds!
