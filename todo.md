[模式：舔毛自检✨]

你说得非常对！我重新整理一个更精准的方案。

## 最终详细技术方案

### 整体架构概述
基于运动相对性的体素运动特征提取器，通过稀疏域内联合处理和空间保持的时空显著性池化，实现高质量的运动特征提取和目标判别能力增强。

---

## 阶段1：稀疏域内联合处理

### 1.1 增强的渐进式感受野编码器
**在现有`ProgressiveReceptiveFieldEncoder`内部实现分离式处理**

#### 1.1.1 特征提取主线（保持现有）
- **第1层（3x3）**：局部细节语义特征提取
- **第2层（5x5）**：中等范围物体级语义特征
- **第3层（7x7）**：大范围场景级上下文特征
- **第4层（3x3）**：多尺度语义信息整合

#### 1.1.2 运动分析支线（在稀疏卷积内联合实现）
**在第4层特征整合后进行运动分析**：
- **时间差分计算**：
  - 利用稀疏张量的时间维度信息
  - 计算相邻时间步的特征变化
  - 提取时间维度运动向量

- **空间梯度计算**：
  - 基于稀疏张量的空间邻域
  - 计算空间维度的梯度场
  - 推断运动的空间方向性

- **运动置信度评估**：
  - 方向一致性：时间稳定性评估
  - 强度可靠性：空间连续性评估
  - 生成运动信息可靠性权重

#### 1.1.3 稀疏域内残差融合
```
最终稀疏特征 = 语义特征 + α × 运动方向特征
```
- 在稀疏域内完成融合，避免额外的数据转换
- 保持稀疏计算的高效性
- 通过可学习权重平衡两种信息

**输出**：
- 运动增强的稀疏特征图
- 运动方向信息编码
- 运动置信度权重

---

## 阶段2：空间保持的时空显著性池化

### 2.1 全局运动基准估计
#### 2.1.1 稀疏域全局运动统计
- **全局运动模式提取**：从稀疏特征中统计全局运动
- **相机运动识别**：识别一致性背景运动模式
- **运动基准建立**：为相对运动计算提供参考

### 2.2 时空运动显著性建模

#### 2.2.1 相对运动分析
- **局部-全局运动分离**：
  ```
  相对运动强度 = 局部运动 - 全局运动基准
  ```
- **目标运动区域识别**：相对运动强的空间位置
- **背景运动抑制**：相对运动弱的区域权重降低

#### 2.2.2 时空关联的轨迹完整性评估
**关键创新：不同时间步对不同空间位置的运动显著性关联**

- **时间-空间运动关联矩阵**：
  ```
  关联权重[t, h, w] = f(运动强度[t, h, w], 轨迹连续性[t-1:t+1, h±δ, w±δ])
  ```
  
- **空间位置的时间轨迹分析**：
  - 每个空间位置(h,w)在不同时间步t的运动模式
  - 基于运动显著性的时间步重要性评估
  - 空间邻域的运动一致性影响

- **动态时空权重计算**：
  - 运动显著的空间位置：对应时间步获得更高权重
  - 运动平缓的空间位置：时间权重相对均匀
  - 噪声区域：所有时间步权重降低

#### 2.2.3 运动显著性引导的时空注意力
- **空间自适应时间注意力**：
  ```
  注意力权重[t, h, w] = softmax(显著性[t, h, w] / temperature)
  ```
- **不同空间位置的个性化时间建模**：
  - 高运动区域：关注运动峰值时间步
  - 稳定区域：均匀关注所有时间步
  - 背景区域：降低整体时间权重

### 2.3 空间保持的显著性池化策略

#### 2.3.1 空间维度强制保持
- **输入**：[B, C, T, H, W]
- **输出**：[B, C, H, W] （H×W维度严格保持）
- **池化约束**：只在时间维度进行压缩

#### 2.3.2 基于运动显著性的时间池化
- **个性化时间权重**：每个空间位置独立的时间注意力
- **显著性引导压缩**：
  ```
  输出[b, c, h, w] = Σ_t (注意力权重[t, h, w] × 输入[b, c, t, h, w])
  ```
- **轨迹信息编码**：将关键时间模式编码到通道维度

#### 2.3.3 时间轨迹记忆机制
- **关键时间步记录**：保留每个空间位置的关键时间信息
- **轨迹模式编码**：将时间轨迹特征编码为额外通道
- **运动历史保持**：确保重要的时间依赖信息不丢失

### 2.4 同步运动物体概率生成

#### 2.4.1 基于时空关联的概率计算
- **轨迹完整性概率**：基于时空关联强度
- **相对运动概率**：基于运动显著性分析
- **空间一致性概率**：基于邻域运动模式

#### 2.4.2 概率图精炼
- **噪声抑制**：低轨迹完整性区域概率降低
- **背景抑制**：基于全局运动一致性
- **边界清晰化**：增强运动物体边界的概率对比

**输出**：
- 运动增强特征：[B, motion_channels, H, W]
- 运动物体概率图：[B, H, W]
- 运动强度评分：[B]

---

## 阶段3：特征标准化输出
**保持原有设计，专注于接口标准化**

---

## 最终精准任务规划

### 第一阶段：稀疏域内联合处理实现 🔧

#### 任务1.1：增强现有稀疏编码器
- **文件**：`lib/models/layers/voxel_motion_extractor.py`
- **类**：`ProgressiveReceptiveFieldEncoder`
- **具体操作**：
  - **保持**：现有的4层渐进式感受野架构
  - **移除**：现有的`motion_direction`模块（转换为密集域后的处理）
  - **新增**：在第4层后添加稀疏域运动分析
    - `sparse_temporal_diff()`：稀疏张量时间差分计算
    - `sparse_spatial_gradient()`：稀疏张量空间梯度计算
    - `motion_confidence_eval()`：运动置信度评估
  - **修改**：`forward()`方法实现稀疏域内残差融合

#### 任务1.2：稀疏域运动分析实现
- **在`ProgressiveReceptiveFieldEncoder`内部实现**：
  - 利用稀疏张量的`indices`信息进行时间差分
  - 基于稀疏张量的邻域关系计算空间梯度
  - 生成运动方向编码和置信度权重
  - 通过残差连接融合到主特征中

### 第二阶段：时空显著性池化重构 🔧🔧

#### 任务2.1：全局运动基准估计
- **修改类**：`VoxelMotionExtractor`
- **新增方法**：`estimate_global_motion_baseline()`
- **实现**：
  - 从密集特征中提取全局运动统计
  - 识别相机运动引起的一致性模式
  - 建立局部-全局运动分离的基准

#### 任务2.2：时空关联显著性建模
- **新增方法**：`compute_spatiotemporal_correlation()`
- **核心实现**：
  - **时间-空间关联矩阵计算**：
    ```python
    # 为每个空间位置(h,w)计算其在不同时间步t的运动显著性关联
    correlation_matrix = torch.zeros(B, T, H, W)
    for t in range(T):
        for h in range(H):
            for w in range(W):
                # 计算该位置在时间t的运动显著性
                # 考虑空间邻域和时间邻域的影响
                correlation_matrix[b, t, h, w] = compute_motion_saliency(
                    motion_features[b, :, t, h, w],
                    spatial_neighbors[h, w],
                    temporal_neighbors[t]
                )
    ```
  - **空间位置个性化时间权重**：
    - 高运动显著性位置：对应时间步权重增强
    - 低运动显著性位置：时间权重平滑化
    - 噪声位置：整体权重抑制

#### 任务2.3：空间保持的显著性池化
- **重构方法**：`VoxelMotionExtractor.forward()`
- **关键实现**：
  - **替换现有时间注意力模块**：
    ```python
    # 原有：简单的时间注意力
    # attention_weights = F.softmax(attention_logits / temperature, dim=2)
    
    # 新设计：基于运动显著性的空间自适应时间注意力
    spatiotemporal_weights = self.compute_spatiotemporal_correlation(dense_features)
    attention_weights = F.softmax(spatiotemporal_weights / self.temporal_temperature, dim=2)
    ```
  - **空间维度强制保持**：
    ```python
    # 确保输出维度严格为 [B, C, H, W]
    assert processed_motion_features.shape[-2:] == (H, W)
    ```
  - **个性化时间池化**：
    ```python
    # 每个空间位置使用独立的时间权重
    processed_motion_features = torch.sum(
        dense_features * attention_weights.unsqueeze(1), dim=2
    )  # [B, C, H, W]
    ```

#### 任务2.4：同步概率图生成
- **集成到池化过程中**：
  - 基于时空关联强度计算轨迹完整性
  - 利用相对运动分析生成运动物体概率
  - 在池化的同时完成概率图的空间对齐

### 第三阶段：接口标准化与优化 🔧

#### 任务3.1：输出格式确保
- **严格保证输出维度**：
  - 运动特征：[B, motion_channels, H, W]
  - 概率图：[B, H, W]
  - 强度评分：[B]

#### 任务3.2：性能优化与测试
- **稀疏计算优化**：确保稀疏域运动分析的高效性
- **内存使用优化**：避免不必要的中间变量
- **端到端测试**：验证整体流程的正确性

---

## 技术实现的核心创新

### 1. **稀疏域内的联合处理**
- 在稀疏卷积类内部完成运动分析
- 避免额外的模块和数据转换开销
- 保持稀疏计算的高效性

### 2. **时空关联的个性化建模**
- 不同空间位置的个性化时间权重
- 基于运动显著性的时空关联分析
- 精确的空间-时间依赖建模

### 3. **空间保持的智能池化**
- 强制保持原始空间分辨率
- 基于运动显著性的自适应时间压缩
- 时间轨迹信息的有效编码

### 4. **运动相对性的有效利用**
- 全局-局部运动的智能分离
- 相机运动的自动抑制
- 基于物理意义的目标识别

