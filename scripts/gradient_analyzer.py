#!/usr/bin/env python3
"""
梯度信息分析工具
用于分析adapter.log中的梯度统计信息，按层统计梯度随epoch变化的情况
"""

import re
import argparse
import matplotlib.pyplot as plt
import numpy as np
from collections import defaultdict
import pandas as pd

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

class GradientAnalyzer:
    def __init__(self, log_file):
        self.log_file = log_file
        self.epochs = []
        self.gradient_data = defaultdict(lambda: defaultdict(list))
        
        # 梯度统计信息的字段映射
        self.field_mapping = {
            0: 'L2_norm',
            1: 'abs_mean', 
            2: 'abs_max',
            3: 'abs_min',
            4: 'mean',
            5: 'std',
            6: 'nan_count',
            7: 'zero_count'
        }
        
    def parse_log(self):
        """解析日志文件"""
        print("Parsing log file...")
        
        current_epoch = None
        epoch_pattern = r'\[train: (\d+), \d+ / \d+\]'
        grad_pattern = r'\[Grad Stats of (module\.backbone\.voxel_injectors\.\d+\..*?)\] L2 norm: ([\d\.e\-\+]+) \| abs_mean: ([\d\.e\-\+]+) \| abs_max: ([\d\.e\-\+]+) \| abs_min: ([\d\.e\-\+]+) \| mean: ([\d\.e\-\+\-]+) \| std: ([\d\.e\-\+]+) \| nan: (\d+) \| zero: (\d+)/\d+'
        
        with open(self.log_file, 'r', encoding='utf-8') as f:
            for line in f:
                # 检查是否是新的epoch
                epoch_match = re.search(epoch_pattern, line)
                if epoch_match:
                    current_epoch = int(epoch_match.group(1))
                    if current_epoch not in self.epochs:
                        self.epochs.append(current_epoch)
                
                # 检查是否是梯度统计信息
                grad_match = re.search(grad_pattern, line)
                if grad_match and current_epoch is not None:
                    module_name = grad_match.group(1)
                    
                    # 提取层名称（去掉数字后的具体子模块）
                    layer_match = re.match(r'module\.backbone\.voxel_injectors\.(\d+)\.(.*)', module_name)
                    if layer_match:
                        layer_idx = layer_match.group(1)
                        sub_module = layer_match.group(2)
                        layer_name = f"voxel_injectors.{layer_idx}"
                        
                        # 解析梯度统计值
                        values = [float(grad_match.group(i)) for i in range(2, 8)]
                        values.extend([int(grad_match.group(8)), int(grad_match.group(9))])
                        
                        # 存储数据
                        for i, value in enumerate(values):
                            field_name = self.field_mapping[i]
                            self.gradient_data[layer_name][field_name].append((current_epoch, value))
        
        print(f"Parsing completed! Found {len(self.epochs)} epochs, {len(self.gradient_data)} layers")
        
    def get_available_fields(self):
        """获取可用的统计字段"""
        return list(self.field_mapping.values())
    
    def get_available_layers(self):
        """获取可用的层"""
        return sorted(self.gradient_data.keys())
    
    def plot_gradient_trends(self, selected_fields, selected_layers=None, save_path=None):
        """绘制梯度变化趋势图"""
        if selected_layers is None:
            selected_layers = self.get_available_layers()
        
        # 创建子图
        n_fields = len(selected_fields)
        fig, axes = plt.subplots(n_fields, 1, figsize=(12, 4*n_fields))
        if n_fields == 1:
            axes = [axes]
        
        colors = plt.cm.tab10(np.linspace(0, 1, len(selected_layers)))
        
        for field_idx, field in enumerate(selected_fields):
            ax = axes[field_idx]
            
            for layer_idx, layer in enumerate(selected_layers):
                if layer in self.gradient_data and field in self.gradient_data[layer]:
                    data = self.gradient_data[layer][field]
                    if data:
                        epochs, values = zip(*data)
                        ax.plot(epochs, values, 'o-', label=layer, color=colors[layer_idx], 
                               linewidth=1.5, markersize=4, alpha=0.8)
            
            ax.set_xlabel('Epoch')
            ax.set_ylabel(field)
            ax.set_title(f'Gradient Statistics: {field}')
            ax.grid(True, alpha=0.3)
            ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            
            # 使用对数坐标（如果值的范围很大）
            if field in ['L2_norm', 'abs_mean', 'abs_max', 'abs_min', 'std']:
                ax.set_yscale('log')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Chart saved to: {save_path}")
        
        plt.show()
    
    def export_data(self, output_file, selected_fields=None, selected_layers=None):
        """导出数据到CSV文件"""
        if selected_fields is None:
            selected_fields = self.get_available_fields()
        if selected_layers is None:
            selected_layers = self.get_available_layers()
        
        # 准备数据
        export_data = []
        for layer in selected_layers:
            if layer in self.gradient_data:
                for field in selected_fields:
                    if field in self.gradient_data[layer]:
                        for epoch, value in self.gradient_data[layer][field]:
                            export_data.append({
                                'Layer': layer,
                                'Field': field,
                                'Epoch': epoch,
                                'Value': value
                            })
        
        # 保存到CSV
        df = pd.DataFrame(export_data)
        df.to_csv(output_file, index=False)
        print(f"Data exported to: {output_file}")
    
    def print_summary(self):
        """打印数据摘要"""
        print("\n=== Gradient Analysis Summary ===")
        print(f"Total epochs: {len(self.epochs)}")
        print(f"Epoch range: {min(self.epochs)} - {max(self.epochs)}")
        print(f"Detected layers: {len(self.gradient_data)}")
        print("\nAvailable layers:")
        for i, layer in enumerate(self.get_available_layers()):
            print(f"  {i}: {layer}")
        print("\nAvailable statistical fields:")
        for i, field in enumerate(self.get_available_fields()):
            print(f"  {i}: {field}")

def main():
    parser = argparse.ArgumentParser(description='梯度信息分析工具')
    parser.add_argument('log_file', help='日志文件路径')
    parser.add_argument('--fields', type=str, help='要分析的字段编号，用逗号分隔 (例如: 0,1,2)')
    parser.add_argument('--layers', type=str, help='要分析的层编号，用逗号分隔 (例如: 0,1,2)')
    parser.add_argument('--save', type=str, help='保存图表的路径')
    parser.add_argument('--export', type=str, help='导出数据的CSV文件路径')
    parser.add_argument('--list', action='store_true', help='只列出可用的字段和层，不进行分析')
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = GradientAnalyzer(args.log_file)
    analyzer.parse_log()
    
    if args.list:
        analyzer.print_summary()
        return
    
    # 选择字段
    available_fields = analyzer.get_available_fields()
    if args.fields:
        field_indices = [int(x.strip()) for x in args.fields.split(',')]
        selected_fields = [available_fields[i] for i in field_indices if i < len(available_fields)]
    else:
        # 默认选择前3个字段
        selected_fields = available_fields[:3]
    
    # 选择层
    available_layers = analyzer.get_available_layers()
    if args.layers:
        layer_indices = [int(x.strip()) for x in args.layers.split(',')]
        selected_layers = [available_layers[i] for i in layer_indices if i < len(available_layers)]
    else:
        # 默认选择前5个层
        selected_layers = available_layers[:5]
    
    print(f"\nSelected fields: {selected_fields}")
    print(f"Selected layers: {selected_layers}")
    
    # 绘制图表
    analyzer.plot_gradient_trends(selected_fields, selected_layers, args.save)
    
    # 导出数据
    if args.export:
        analyzer.export_data(args.export, selected_fields, selected_layers)
    
    # 打印摘要
    analyzer.print_summary()

if __name__ == '__main__':
    main()
