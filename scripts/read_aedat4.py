"""
DAVIS事件相机数据处理脚本

从.aedat4文件中提取事件数据和APS图像，转换为标准图像格式和文本文件
支持两种时间戳模式：曝光时间模式和帧间隔模式
"""

from dv import AedatFile
import cv2
import os
import numpy as np
from PIL import Image
import pdb


def extract_davis(aedat_file_path, filename_txt_path, croped_events_path, save_path_dvs, save_path_aps, dvs_img_interval):
    """
    从DAVIS相机数据文件中提取事件数据和APS图像
    
    Args:
        aedat_file_path: .aedat4数据文件路径
        filename_txt_path: 时间戳输出文件路径
        croped_events_path: 事件数据输出文件路径
        save_path_dvs: 事件图像保存路径
        save_path_aps: APS图像保存路径
        dvs_img_interval: 图像采样间隔
    """
    frame_no = 0
    frame_all = []
    frame_exposure_time = []
    frame_interval_time = []
    use_mode = 'frame_exposure_time'  # 默认使用曝光时间模式
    
    with AedatFile(aedat_file_path) as f:
        print(f.names)
        
        # 提取每帧的时间戳信息
        for frame in f['frames']:
            frame_all.append(frame.image)   
            frame_exposure_time.append([frame.timestamp_start_of_exposure, frame.timestamp_end_of_exposure])
            frame_interval_time.append([frame.timestamp_start_of_frame, frame.timestamp_end_of_frame])
        
        # 选择时间戳模式
        if use_mode == 'frame_exposure_time':
            frame_timestamp = frame_exposure_time
        elif use_mode == 'frame_interval_time':
            frame_timestamp = frame_interval_time

        frame_num = len(frame_timestamp)
        height, width = f['events'].size       
        event_frame = 255 * np.ones((height, width, 3), dtype=np.uint8)
        
        # 计算采样帧索引
        idx = np.round(np.linspace(0, len(frame_timestamp) - 1, int(frame_num/dvs_img_interval))).astype(int)
        frame_timestamp = np.array(frame_timestamp)[idx]
        
        file = open(filename_txt_path, 'w')

        # 批量读取所有事件数据以提高处理速度
        events = np.hstack([packet for packet in f['events'].numpy()])
        timestamps, x, y, polarities = events['timestamp'], events['x'], events['y'], events['polarity']

        # 保存所有事件数据到文本文件
        event_file = open(croped_events_path, 'w')
        for ii in range(timestamps.shape[0]):
            event_file.write(('{}, {}, {}, {}'.format(timestamps[ii], x[ii], y[ii], polarities[ii]) + "\n"))
        event_file.close()

        # 生成事件图像序列
        for frame_no in range(0, int(frame_num/dvs_img_interval)-1):
            event_frame = 255 * np.ones((height, width, 3), dtype=np.uint8)
            
            # 根据时间窗口提取当前帧的事件
            start_idx = np.where(events['timestamp'] >= frame_timestamp[frame_no][0])[0][0]
            end_idx   = np.where(events['timestamp'] >= frame_timestamp[frame_no][1])[0][0]
            event = events[start_idx:end_idx]
            
            # 分别处理正极性和负极性事件
            on_idx  = np.where(event['polarity'] == 1)  # 正极性事件索引
            off_idx = np.where(event['polarity'] == 0)  # 负极性事件索引
            
            # 颜色编码：正极性为蓝色，负极性为红色
            event_frame[event['y'][on_idx],  event['x'][on_idx], :]  = [30, 30, 220] * event['polarity'][on_idx][:, None]
            event_frame[event['y'][off_idx], event['x'][off_idx], :] = [200, 30, 30] * (event['polarity'][off_idx]+1)[:, None]
            
            # 保存事件图像
            cv2.imwrite(os.path.join(save_path_dvs, 'frame{:04d}'.format(frame_no*dvs_img_interval)+'.bmp'), event_frame)
            cv2.imshow('Event Image', event_frame)
            cv2.waitKey(1)
            print('The {} timestamp is {}'.format(frame_no, events['timestamp'][frame_no]))
            file.write('The {} frame, the timestamp is {}'.format(frame_no, events['timestamp'][frame_no]) + "\n")
            
        file.close()

        # 保存APS图像序列
        for frame_no in range(0, int(frame_num/dvs_img_interval)-1):
            this_frame = frame_all[frame_no]
            cv2.imwrite(os.path.join(save_path_aps, 'frame{:04d}'.format(frame_no)+'.bmp'), this_frame)
            cv2.imshow('APS Image', this_frame)
            cv2.waitKey(1)


def extract_rgb(rgb_file_path, save_path_rgb):
    """
    从RGB视频文件中提取帧序列并保存为图像文件
    
    Args:
        rgb_file_path: RGB视频文件路径
        save_path_rgb: 图像保存路径
    """
    cap = cv2.VideoCapture(rgb_file_path)
    frame_no = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        cv2.imwrite(os.path.join(save_path_rgb, 'frame{:04d}'.format(frame_no)+'.bmp'), frame)
        frame_no = frame_no + 1


def main():
    """
    批量处理指定目录下的所有.aedat4文件
    """
    aedat_path = '/home/<USER>/Downloads'
    dvs_img_interval = 1
    
    for root, dirs, files in os.walk(aedat_path):
        for file in files:
            (filename, extension) = os.path.splitext(file)
            print("==>> filename: ", filename)

            if (extension == '.aedat4'):
                # 检查是否已经处理过，避免重复处理
                if filename+'_aps' in os.listdir(root) or filename+'_dvs' in os.listdir(root):
                    print("==>> Skip this video ... ")
                    continue 
                
                # 设置输出路径
                save_path_dvs = os.path.join(root, filename, filename+'_dvs')
                save_path_aps = os.path.join(root, filename, filename+'_aps')
                aedat_file_path = os.path.join(root, filename+'.aedat4')
                filename_txt_path = os.path.join(root, filename+'_timestamp.txt')
                croped_events_path = os.path.join(root, filename+'_events.txt')
                
                # 创建输出目录
                if not os.path.exists(save_path_dvs):
                    os.makedirs(save_path_dvs)
                if not os.path.exists(save_path_aps):
                    os.makedirs(save_path_aps)

                extract_davis(aedat_file_path, filename_txt_path, croped_events_path, save_path_dvs, save_path_aps, dvs_img_interval)


if __name__ == '__main__':
    main()





