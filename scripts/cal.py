import subprocess

# 要保留的PID列表
keep_pids = {'3919', '17182', '17183', '17184', '17185'}

# 获取nvidia-smi输出
result = subprocess.check_output("nvidia-smi", shell=True).decode()

# 提取所有GPU进程的PID
pids = set()
for line in result.splitlines():
    if ' C ' in line:
        parts = line.split()
        if len(parts) > 5:
            pid = parts[4]
            if pid.isdigit():
                pids.add(pid)

# 杀死不在保留列表中的进程
for pid in pids:
    if pid not in keep_pids:
        try:
            subprocess.run(["kill", "-9", pid])
            print(f"Killed PID {pid}")
        except Exception as e:
            print(f"Failed to kill PID {pid}: {e}")