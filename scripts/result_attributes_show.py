import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import os
from tabulate import tabulate

def visualize_attribute_results(csv_file, output_dir=None, top_n=None, 
                               visualization_types=["table", "heatmap", "barplot"]):
    """
    读取并可视化跟踪器属性分析结果的CSV文件
    
    参数:
        csv_file (str): CSV文件的路径
        output_dir (str, optional): 图表输出目录。默认为CSV文件所在目录下的'visualizations'文件夹
        top_n (int, optional): 只显示前N名跟踪器。默认为None，显示所有跟踪器
        visualization_types (list): 要生成的可视化类型，可包含"table", "heatmap", "barplot"
    
    返回:
        DataFrame: 包含属性分析结果的数据框
    """
    # 读取CSV文件
    df = pd.read_csv(csv_file)
    
    # 设置Tracker列为索引
    df.set_index('Tracker', inplace=True)
    
    # 如果需要，只保留前N名跟踪器
    if top_n is not None and top_n > 0:
        # 按Overall列排序
        df = df.sort_values(by='Overall', ascending=False).head(top_n)
    
    # 如果没有指定输出目录，创建一个默认目录
    if output_dir is None:
        output_dir = os.path.join(os.path.dirname(csv_file), 'visualizations')
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取文件名（不含扩展名）作为基础名
    base_name = os.path.splitext(os.path.basename(csv_file))[0]
    
    # 生成表格
    if "table" in visualization_types:
        # 使用tabulate生成格式化表格
        table_str = tabulate(df, headers=df.columns, tablefmt="grid", floatfmt=".3f")
        table_file = os.path.join(output_dir, f"{base_name}_table.txt")
        with open(table_file, 'w') as f:
            f.write(table_str)
        print(f"表格已保存至: {table_file}")
        print("\n属性分析结果表格:")
        print(table_str)
        
        # 生成表格PNG图片
        # 创建一个没有边框的图表
        plt.figure(figsize=(max(12, len(df.columns) * 1.5), max(8, len(df) * 0.5)))
        ax = plt.subplot(111, frame_on=False)
        ax.xaxis.set_visible(False)  # 隐藏x轴
        ax.yaxis.set_visible(False)  # 隐藏y轴
        
        # 创建表格，cellText是表格内容，colLabels是列标签
        cell_text = []
        for idx in df.index:
            cell_text.append([f"{x:.3f}" for x in df.loc[idx].values])
        
        # 创建二维颜色数组，与表格大小一致
        row_count = len(df)
        col_count = len(df.columns)
        color_matrix = np.zeros((row_count, col_count, 4))  # RGBA颜色矩阵
        
        # 使用渐变色填充颜色矩阵
        for i in range(row_count):
            color_row = plt.cm.YlGnBu(np.linspace(0.3, 0.9, col_count))
            color_matrix[i] = color_row
        
        table = plt.table(cellText=cell_text,
                          rowLabels=df.index,
                          colLabels=df.columns,
                          cellLoc='center',
                          loc='center',
                          cellColours=color_matrix)
        
        # 设置表格样式
        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 1.5)  # 调整表格大小
        
        # 设置标题
        plt.title(f'Attribute Analysis Table - {base_name}', fontsize=16, pad=20)
        plt.tight_layout()
        
        # 保存表格图片
        table_img_file = os.path.join(output_dir, f"{base_name}_table.png")
        plt.savefig(table_img_file, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"表格图片已保存至: {table_img_file}")
    
    # 生成热力图
    if "heatmap" in visualization_types:
        plt.figure(figsize=(20, 12))
        
        # 创建热力图
        sns.heatmap(df, annot=True, cmap="YlGnBu", fmt='.3f', 
                    linewidths=.5, cbar_kws={'label': 'Performance'})
        
        plt.title(f'Attribute Analysis Heatmap - {base_name}', fontsize=16)
        plt.tight_layout()
        
        # 保存图表
        heatmap_file = os.path.join(output_dir, f"{base_name}_heatmap.png")
        plt.savefig(heatmap_file, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"热力图已保存至: {heatmap_file}")
    
    # 生成柱状图
    if "barplot" in visualization_types:
        # 获取前五名跟踪器用于柱状图比较
        top_df = df.head(min(5, len(df)))
        
        # 设置风格
        sns.set(style="whitegrid")
        
        # 生成每个属性的柱状图
        for col in df.columns:
            plt.figure(figsize=(12, 8))
            
            # 创建柱状图
            ax = sns.barplot(x=top_df.index, y=top_df[col], palette="muted")
            
            # 设置图表标题和标签
            plt.title(f'Top 5 on {col}', fontsize=16)
            plt.ylabel('Scores', fontsize=14)
            plt.xlabel('Trackers', fontsize=14)
            
            # 旋转x轴标签以防重叠
            plt.xticks(rotation=45, ha='right')
            
            # 在柱子上显示具体数值
            for i, p in enumerate(ax.patches):
                ax.annotate(f'{p.get_height():.3f}', 
                           (p.get_x() + p.get_width() / 2., p.get_height()), 
                           ha = 'center', va = 'bottom', 
                           xytext = (0, 5), textcoords = 'offset points')
            
            plt.tight_layout()
            
            # 保存图表
            barplot_file = os.path.join(output_dir, f"{base_name}_{col}_barplot.png")
            plt.savefig(barplot_file, dpi=300, bbox_inches='tight')
            plt.close()
            
        print(f"柱状图已保存至: {output_dir}")
    
    # 生成雷达图 (对前5个跟踪器)
    if "radar" in visualization_types:
        # 获取前五名跟踪器用于雷达图比较
        top_df = df.head(min(5, len(df)))
        
        # 排除'Overall'列，只保留属性列
        if 'Overall' in top_df.columns:
            cols = [col for col in top_df.columns if col != 'Overall']
        else:
            cols = top_df.columns
            
        # 将数据转换为雷达图所需格式
        categories = cols
        N = len(categories)
        
        # 创建角度
        angles = [n / float(N) * 2 * np.pi for n in range(N)]
        angles += angles[:1]  # 闭合雷达图
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(12, 12), subplot_kw=dict(polar=True))
        
        # 为每个跟踪器绘制雷达图
        for i, tracker in enumerate(top_df.index):
            values = top_df.loc[tracker, cols].tolist()
            values += values[:1]  # 闭合雷达图
            
            # 绘制雷达线
            ax.plot(angles, values, linewidth=2, label=tracker)
            # 填充区域
            ax.fill(angles, values, alpha=0.1)
        
        # 设置刻度标签
        plt.xticks(angles[:-1], categories, size=12)
        
        # 设置y轴标签
        ax.set_rlabel_position(0)
        plt.yticks([0.2, 0.4, 0.6, 0.8], ["0.2", "0.4", "0.6", "0.8"], size=10)
        plt.ylim(0, 1)
        
        # 添加图例
        plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
        
        plt.title(f'Attribute Analysis Radar - {base_name}', size=16)
        plt.tight_layout()
        
        # 保存图表
        radar_file = os.path.join(output_dir, f"{base_name}_radar.png")
        plt.savefig(radar_file, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"雷达图已保存至: {radar_file}")
    
    return df

# 使用示例
if __name__ == "__main__":
    # 假设CSV文件路径为 "./res_fig/overlap_AUC_attribute_results.csv"
    csv_path = "figures/visevent/overlap_AUC_attribute_results.csv"
    
    # 可视化文件内容，显示前10名跟踪器，生成所有类型的可视化
    df = visualize_attribute_results(
        csv_path, 
        top_n=10,
        # visualization_types=["table", "heatmap", "barplot", "radar"]
        visualization_types=["table", "heatmap"]
    )

    csv_path = "figures/visevent/error_AUC_attribute_results.csv"
    
    # 可视化文件内容，显示前10名跟踪器，生成所有类型的可视化
    df = visualize_attribute_results(
        csv_path, 
        top_n=10,
        # visualization_types=["table", "heatmap", "barplot", "radar"]
        visualization_types=["table", "heatmap"]
    )
