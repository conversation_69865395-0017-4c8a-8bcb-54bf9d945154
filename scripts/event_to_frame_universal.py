"""
通用DAVIS事件相机数据处理脚本

从.aedat4文件中提取事件数据，转换为事件图像帧
参考read_aedat4.py中的事件图像生成方法
支持COESOT、VisEvent、FE108等多种数据集格式
"""

import os
import sys
import numpy as np
import argparse
from tqdm import tqdm
import cv2
from dv import AedatFile


def create_event_frame_optimized(events, height, width, background_color=127,
                                positive_color=255, negative_color=0):
    """
    优化的事件图像帧生成函数
    
    Args:
        events: 预过滤的事件数据
        height: 图像高度
        width: 图像宽度
        background_color: 背景颜色
        positive_color: 正极性事件颜色
        negative_color: 负极性事件颜色
    Returns:
        event_frame: 生成的事件图像帧
    """
    # 使用np.full预分配背景
    event_frame = np.full((height, width), background_color, dtype='uint8')
    
    if len(events) == 0:
        return event_frame
    
    # 使用布尔索引快速分类
    positive_mask = events['polarity'] == 1
    negative_mask = events['polarity'] == 0
    
    if np.any(positive_mask):
        event_frame[events['y'][positive_mask], events['x'][positive_mask]] = positive_color
    
    if np.any(negative_mask):
        event_frame[events['y'][negative_mask], events['x'][negative_mask]] = negative_color
    
    return event_frame

def extract_event_frames(aedat_file_path, save_path_frames, dvs_img_interval=1, 
                        use_mode='frame_exposure_time', max_frames=None,
                        save_timestamps=False, timestamp_file=None, batch_size=32):
    """
    从DAVIS相机数据文件中提取事件数据并生成事件图像帧（批处理并行优化）
    
    Args:
        aedat_file_path: .aedat4数据文件路径
        save_path_frames: 事件图像帧保存路径
        dvs_img_interval: 图像采样间隔
        use_mode: 时间戳模式 ('frame_exposure_time' 或 'frame_interval_time')
        max_frames: 最大处理帧数 (None表示处理所有帧)
        save_timestamps: 是否保存时间戳信息
        timestamp_file: 时间戳文件保存路径
        batch_size: 批处理大小
    """
    frame_all = []
    frame_exposure_time = []
    frame_interval_time = []
    
    with AedatFile(aedat_file_path) as f:
        print(f"数据流名称: {f.names}")
          # 提取每帧的时间戳信息
        for frame in f['frames']:
            frame_all.append(frame.image)   
            frame_exposure_time.append([frame.timestamp_start_of_exposure, frame.timestamp_end_of_exposure])
            frame_interval_time.append([frame.timestamp_start_of_frame, frame.timestamp_end_of_frame])
        
        # 选择时间戳模式
        if use_mode == 'frame_exposure_time' and frame_exposure_time:
            frame_timestamp = frame_exposure_time
        elif use_mode == 'frame_interval_time' and frame_interval_time:
            frame_timestamp = frame_interval_time
        elif frame_exposure_time:
            frame_timestamp = frame_exposure_time
            print("警告: 使用默认的曝光时间模式")
        elif frame_interval_time:
            frame_timestamp = frame_interval_time
            print("警告: 使用帧间隔时间模式")
        else:
            print("错误: 没有找到有效的时间戳数据")
            return

        frame_num = len(frame_timestamp)
        if frame_num == 0:
            print("警告: 没有找到帧数据")
            return
            
        height, width = f['events'].size
        print(f"事件相机分辨率: {width}x{height}")
        
        # 计算采样帧索引
        sampled_frame_count = max(1, int(frame_num / dvs_img_interval))
        if max_frames and sampled_frame_count > max_frames:
            sampled_frame_count = max_frames
            
        idx = np.round(np.linspace(0, frame_num - 1, sampled_frame_count)).astype(int)
        frame_timestamp = np.array(frame_timestamp)[idx]
        
        # 批量读取和预处理所有事件数据
        print("批量读取事件数据...")
        events = np.hstack([packet for packet in f['events'].numpy()])
        
        # 预先进行全局坐标有效性过滤
        valid_x = (events['x'] >= 0) & (events['x'] < width)
        valid_y = (events['y'] >= 0) & (events['y'] < height)
        global_valid_mask = valid_x & valid_y
        
        if np.any(global_valid_mask):
            filtered_events = events[global_valid_mask]
            timestamps = filtered_events['timestamp']
            print(f"原始事件数量: {len(events)}, 有效事件数量: {len(filtered_events)}")
        else:
            print("警告: 没有有效的事件数据")
            return
        
        print(f"总帧数: {frame_num}, 采样后帧数: {len(frame_timestamp)}")
        
        # 批量预计算所有帧索引，使用向量化操作（优化版）
        print("批量预计算帧索引...")
        frame_timestamps_array = np.array(frame_timestamp)
        start_times = frame_timestamps_array[:, 0]
        end_times = frame_timestamps_array[:, 1]
        
        # 使用向量化searchsorted（一次性计算所有索引）
        all_times = np.concatenate([start_times, end_times])
        all_indices = np.searchsorted(timestamps, all_times, side='left')
        
        # 分离开始和结束索引
        n_frames = len(start_times)
        start_indices = all_indices[:n_frames]
        end_indices = all_indices[n_frames:]
        
        # 筛选有效帧（向量化条件检查）
        valid_mask = (start_indices < len(timestamps)) & (end_indices <= len(timestamps)) & (start_indices < end_indices)        # 批量计算事件数量，用于后续优化
        event_counts = end_indices - start_indices
        valid_mask &= (event_counts > 0)  # 过滤空帧
        
        frame_indices = [(i, start_indices[i], end_indices[i], start_times[i], end_times[i], event_counts[i]) 
                        for i in range(len(start_times)) if valid_mask[i]]
        
        print(f"有效帧数: {len(frame_indices)}")
        
        # 初始化时间戳文件
        timestamp_writer = None
        if save_timestamps and timestamp_file:
            timestamp_writer = open(timestamp_file, 'w')
            timestamp_writer.write("frame_no,start_timestamp,end_timestamp,event_count\n")
        
        # 批处理生成事件图像序列
        adaptive_batch_size = min(batch_size, len(frame_indices))
        with tqdm(total=len(frame_indices), desc="生成事件图像帧") as pbar:
            for batch_idx in range(0, len(frame_indices), adaptive_batch_size):
                batch_frames = frame_indices[batch_idx:batch_idx+adaptive_batch_size]
                  # 批量处理当前批次的帧
                for frame_data in batch_frames:
                    frame_no, start_idx, end_idx, start_time, end_time, event_count = frame_data
                    try:
                        # 验证索引有效性
                        if start_idx >= len(filtered_events) or end_idx > len(filtered_events) or start_idx >= end_idx:
                            print(f"无效帧 {frame_no}: 索引范围 [{start_idx}, {end_idx})")
                            event_frame = np.full((height, width), 127, dtype='uint8')  # 默认灰色背景
                            frame_events = []
                        else: 
                            # 直接使用预过滤的事件数据
                            frame_events = filtered_events[start_idx:end_idx]
                            
                            # 创建事件图像帧
                            event_frame = create_event_frame_optimized(frame_events, height, width)
                        
                        # 保存事件图像
                        frame_filename = f'frame{frame_no:04d}.bmp'
                        cv2.imwrite(os.path.join(save_path_frames, frame_filename), event_frame)
                        
                        # 保存时间戳信息
                        if timestamp_writer:
                            timestamp_writer.write(f"{frame_no},{start_time},{end_time},{len(frame_events)}\n")
                        
                        # 更新进度条
                        pbar.set_postfix({
                            'frame': frame_no,
                            'events': len(frame_events),
                            'batch': f"{batch_idx//adaptive_batch_size + 1}"
                        })
                        pbar.update(1)
                        
                    except (IndexError, ValueError) as e:
                        print(f"处理帧 {frame_no} 时出错: {e}")
                        pbar.update(1)
                        continue
                
                # 定期清理内存
                if batch_idx % (adaptive_batch_size * 4) == 0:
                    import gc
                    gc.collect()
        
        # 关闭时间戳文件
        if timestamp_writer:
            timestamp_writer.close()
        
        print("事件图像生成完成")



def process_dataset(data_path, save_path, dataset_type='coesot', dvs_img_interval=1, 
                   use_mode='frame_exposure_time', skip_existing=True, batch_size=32):
    """
    批量处理数据集（批处理并行优化）
    
    Args:
        data_path: 数据集根目录
        save_path: 输出根目录
        dataset_type: 数据集类型 ('coesot', 'visevent', 'fe108')
        dvs_img_interval: 采样间隔
        use_mode: 时间戳模式
        skip_existing: 是否跳过已存在的输出
        batch_size: 批处理大小
    """
    
    # 根据数据集类型设置跳过列表
    skip_videos = []
    if dataset_type == 'coesot':
        skip_videos = [
            'dvSave-2022_01_26_20_01_46', 'dvSave-2022_03_21_11_09_50',
            'dvSave-2022_03_21_16_11_40', 'dvSave-2022_03_21_11_12_27',
            'dvSave-2022_03_21_11_19_37', 'dvSave-2022_02_25_13_21_41',
            'dvSave-2022_02_25_13_31_18', 'dvSave-2022_03_21_09_05_49'
        ]
    
    video_files = os.listdir(data_path)
    
    for videoID, foldName in enumerate(video_files):
        print(f"==>> 处理文件夹 [{videoID+1}/{len(video_files)}]: {foldName}")
        
        # 检查是否为目录
        folder_path = os.path.join(data_path, foldName)
        if not os.path.isdir(folder_path):
            print(f"==>> 跳过文件: {foldName}")
            continue
            
        # 跳过指定的视频
        if foldName in skip_videos:
            print(f"==>> 跳过视频: {foldName}")
            continue

        # 设置输出目录
        if dataset_type == 'coesot':
            frame_save_dir = os.path.join(save_path, foldName, foldName+'_eimg')
            aedat4_file = foldName + '.aedat4'
            aedat_file_path = os.path.join(data_path, foldName, aedat4_file)
        else:  # visevent, fe108等
            frame_save_dir = os.path.join(save_path, foldName, foldName+'_eimg')
            # 查找aedat4文件
            fileLIST = os.listdir(folder_path)
            aedat4_files = [f for f in fileLIST if f.endswith('.aedat4')]
            if not aedat4_files:
                print(f"==>> 在 {foldName} 中找不到aedat4文件")
                continue
            aedat4_file = aedat4_files[0]  # 取第一个aedat4文件            
            aedat_file_path = os.path.join(folder_path, aedat4_file)
        
        # 检查输出目录是否已存在
        if skip_existing and os.path.exists(frame_save_dir) and len(os.listdir(frame_save_dir)) > 0:
            print(f"==>> 事件图像帧已存在，跳过: {foldName}")
            continue
        
        # 创建输出目录
        if not os.path.exists(frame_save_dir):
            os.makedirs(frame_save_dir)
        
        # 检查aedat4文件是否存在
        if not os.path.exists(aedat_file_path):
            print(f"==>> 找不到aedat4文件: {aedat_file_path}")
            continue
        
        try:
            # 设置时间戳文件
            timestamp_file = os.path.join(frame_save_dir, 'timestamps.csv')
            
            # 提取事件图像帧（支持批处理）
            extract_event_frames(
                aedat_file_path=aedat_file_path,
                save_path_frames=frame_save_dir,
                dvs_img_interval=dvs_img_interval,
                use_mode=use_mode,
                save_timestamps=True,
                timestamp_file=timestamp_file,
                batch_size=batch_size
            )
            print(f"==>> 完成处理: {foldName}")
            
        except Exception as e:
            print(f"==>> 处理 {foldName} 时出错: {e}")
            continue

    print("==>> 所有文件处理完成")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='将DAVIS事件数据转换为事件图像帧（批处理并行优化）')
    parser.add_argument('--data_path', type=str, help='数据集根目录')
    parser.add_argument('--save_path', type=str, help='输出根目录')
    parser.add_argument('--dataset_type', type=str, default='coesot', 
                       choices=['coesot', 'visevent', 'fe108'], help='数据集类型')
    parser.add_argument('--interval', type=int, default=1, help='图像采样间隔')
    parser.add_argument('--mode', type=str, default='frame_exposure_time',
                       choices=['frame_exposure_time', 'frame_interval_time'], 
                       help='时间戳模式')
    parser.add_argument('--no_skip', action='store_true', help='不跳过已存在的输出')
    parser.add_argument('--batch_size', type=int, default=32, help='批处理大小')
    
    args = parser.parse_args()
    
    # 如果没有提供必需的参数，使用默认配置
    if args.data_path is None or args.save_path is None:
        print("使用默认配置...")
        # 默认配置
        data_path = r"/home/<USER>/STU/workspaces/ruihui/RTrack/data/COESOT/train"
        save_path = r"/home/<USER>/STU/workspaces/ruihui/RTrack/data/COESOT/train"
        dataset_type = 'coesot'
        dvs_img_interval = 1
        use_mode = 'frame_exposure_time'
        skip_existing = True
        batch_size = 32
    else:
        data_path = args.data_path
        save_path = args.save_path
        dataset_type = args.dataset_type
        dvs_img_interval = args.interval
        use_mode = args.mode
        skip_existing = not args.no_skip
        batch_size = args.batch_size
    
    print(f"数据路径: {data_path}")
    print(f"输出路径: {save_path}")
    print(f"数据集类型: {dataset_type}")
    print(f"采样间隔: {dvs_img_interval}")
    print(f"时间戳模式: {use_mode}")
    print(f"跳过已存在: {skip_existing}")
    print(f"批处理大小: {batch_size}")
    
    process_dataset(
        data_path=data_path,
        save_path=save_path,
        dataset_type=dataset_type,
        dvs_img_interval=dvs_img_interval,
        use_mode=use_mode,
        skip_existing=skip_existing,
        batch_size=batch_size
    )
