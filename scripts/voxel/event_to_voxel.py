import os
import pdb
# import csv
import numpy as np
import cv2
import torch
import pandas as pd
from tqdm import tqdm
import time
import scipy.io
from spconv.pytorch.utils import PointToVoxel
from dv import AedatFile
import numpy as np
from scipy.ndimage import gaussian_filter
import json
from collections import defaultdict


def transform_points_to_voxels_enhanced(data_dict={}, voxel_generator=None, device=torch.device("cuda:0"),
                                         preserve_order=True, save_voxel=5000):

    points = data_dict['points']
    
    # 根据preserve_order决定是否打乱点
    if not preserve_order:
        shuffle_idx = np.random.permutation(points.shape[0])
        points = points[shuffle_idx]
    
    data_dict['points'] = points

    # 使用spconv生成voxel输出
    points = torch.as_tensor(data_dict['points'], dtype=torch.float32).to(device)
    voxel_output = voxel_generator(points)

    # 假设一份点云数据是N*4，那么经过pillar生成后会得到三份数据
    # voxels代表了每个生成的voxel数据，维度是[M, 5, 4]
    # coordinates代表了每个生成的voxel所在的zyx轴坐标，维度是[M,3]
    # num_points代表了每个生成的voxel中有多少个有效的点维度是[m,]，因为不满5会被0填充
    voxels, coordinates, num_points = voxel_output
    voxels = voxels.to(device)
    coordinates = coordinates.to(device)
    num_points = num_points.to(device)
    
    # 选event数量在前5000的voxel
    if num_points.shape[0] < save_voxel:
        features = voxels[:, :, 3]
        coor = coordinates[:, :]
    else:
        _, voxels_idx = torch.topk(num_points, save_voxel)
        # 将每个voxel的点的极性拼接作为voxel初始特征
        features = voxels[voxels_idx][:, :, 3]
        # 前5000个voxel的三维坐标
        coor = coordinates[voxels_idx]
    
    # 将y.x.t改为t,x,y
    coor[:, [0, 1, 2]] = coor[:, [2, 1, 0]]

    return coor, features

def create_voxel_generator_pool(device):
    """创建预定义的体素生成器池"""
    generators = {}
    # 高运动场景：细时间分辨率
    generators['high_motion'] = PointToVoxel(
        vsize_xyz=[25, 10, 10],
        coors_range_xyz=[0, 0, 0, 1000, 345, 259],
        num_point_features=4,
        max_num_voxels=20000,
        max_num_points_per_voxel=16,
        device=device
    )
    # 中等运动场景：平衡配置
    generators['medium_motion'] = PointToVoxel(
        vsize_xyz=[40, 10, 10],
        coors_range_xyz=[0, 0, 0, 1000, 345, 259],
        num_point_features=4,
        max_num_voxels=16000,
        max_num_points_per_voxel=16,
        device=device
    )
    # 低运动场景：粗时间分辨率
    generators['low_motion'] = PointToVoxel(
        vsize_xyz=[50, 10, 10],
        coors_range_xyz=[0, 0, 0, 1000, 345, 259],
        num_point_features=4,
        max_num_voxels=12000,
        max_num_points_per_voxel=16,
        device=device
    )
    # 稀疏场景：大体素
    generators['sparse'] = PointToVoxel(
        vsize_xyz=[100, 15, 15],
        coors_range_xyz=[0, 0, 0, 1000, 345, 259],
        num_point_features=4,
        max_num_voxels=8000,
        max_num_points_per_voxel=16,
        device=device
    )
    # 空场景：默认配置
    generators['empty'] = generators['sparse']
    
    return generators

def get_adaptive_save_voxel(scene_type, base_save_voxel=6000):
    """根据场景类型获取自适应的保存体素数量"""
    multipliers = {
        'high_motion': 1.5,
        'medium_motion': 1.0,
        'low_motion': 0.8,
        'sparse': 0.6,
        'empty': 0.5
    }
    
    return int(base_save_voxel * multipliers.get(scene_type, 1.0))

def calculate_motion_intensity(events, time_window=1000, use_filters=False):
    """快速计算运动强度指标"""
    if len(events) < 10:
        return 0.0
    
    timestamps = events['timestamp']
    time_span = timestamps.max() - timestamps.min() + 1
    base_density = len(events) / time_span
    motion_score = 1 - np.exp(-base_density * 1000) 

    if use_filters:
        x_coords = events['x']
        y_coords = events['y']
        polarities = events['polarity']
        # 1. 极性一致性滤波（无经验值）——先验认为极性分布均匀的事件更有意义
        pos_count = np.sum(polarities == 1)
        neg_count = np.sum(polarities == 0)
        total_count = pos_count + neg_count
        
        if total_count == 0:
            return 0.0
        
        # 使用香农熵衡量极性分布的均匀性
        if pos_count > 0 and neg_count > 0:
            pos_ratio = pos_count / total_count
            neg_ratio = neg_count / total_count
            polarity_entropy = -(pos_ratio * np.log2(pos_ratio) + neg_ratio * np.log2(neg_ratio))
            polarity_score = polarity_entropy / 1.0  # 最大熵为1
        else:
            polarity_score = 0.0  # 单极性事件，可能是噪声

        # 2. 空间分布滤波（基于数据分布特征）：先验认为
        # 使用四分位距判断空间分布是否合理
        x_q75, x_q25 = np.percentile(x_coords, [75, 25])
        y_q75, y_q25 = np.percentile(y_coords, [75, 25])
        
        x_iqr = x_q75 - x_q25
        y_iqr = y_q75 - y_q25
        
        # 相对于图像尺寸的分布范围
        x_coverage = x_iqr / 345
        y_coverage = y_iqr / 259
        
        # 使用几何平均评估整体空间覆盖
        spatial_coverage = np.sqrt(x_coverage * y_coverage)

        # 3. 时间连续性滤波（基于事件间隔的统计特征）
        if len(timestamps) > 1:
            time_diffs = np.diff(timestamps)
            
            # 使用变异系数的倒数衡量时间连续性
            # 变异系数越小，时间分布越规律（可能是噪声）
            mean_diff = np.mean(time_diffs)
            std_diff = np.std(time_diffs)
            
            if mean_diff > 0:
                cv_time = std_diff / mean_diff
                # 使用sigmoid函数将CV映射到0-1，避免硬阈值
                temporal_score = 2 / (1 + np.exp(-2 * cv_time))  # sigmoid变换
            else:
                temporal_score = 0.0
        else:
            temporal_score = 0.0

        # 综合分数计算（使用调和平均，对任一低分更敏感）
        scores = [polarity_score, spatial_coverage, temporal_score]
        scores = [max(0.01, s) for s in scores]  # 避免除零
        harmonic_mean = len(scores) / sum(1/s for s in scores)
        motion_score = harmonic_mean * motion_score
    
    return motion_score


def classify_scene_type(events):
    """将场景分类为几种预定义类型"""
    if len(events) == 0:
        return 'empty'
    
    event_count = len(events)
    motion_intensity = calculate_motion_intensity(events, use_filters=False)
    
    if event_count > 50000 and motion_intensity > 0.6:
        return 'high_motion'
    elif event_count > 25000 and motion_intensity > 0.3:
        return 'medium_motion'
    elif event_count > 8000:
        return 'low_motion'
    else:
        return 'sparse'

def create_voxel_statistics():
    """创建体素统计数据结构"""
    return {
        'bins': ['<1024', '1024-2048', '2048-4096', '4096-8192', '>8192'],
        'bin_ranges': [(0, 1024), (1024, 2048), (2048, 4096), (4096, 8192), (8192, float('inf'))],
        'video_stats': {},
        'global_stats': {
            'total_frames': 0,
            'bin_counts': [0, 0, 0, 0, 0],
            'bin_percentages': [0.0, 0.0, 0.0, 0.0, 0.0]
        }
    }

def update_voxel_statistics(stats, video_name, frame_no, voxel_count):
    """更新体素统计信息"""
    if video_name not in stats['video_stats']:
        stats['video_stats'][video_name] = {
            'frames': [],
            'voxel_counts': [],
            'bin_counts': [0, 0, 0, 0, 0],
            'total_frames': 0
        }
    
    # 确定体素数量所属的分段
    bin_idx = 0
    for i, (min_val, max_val) in enumerate(stats['bin_ranges']):
        if min_val <= voxel_count < max_val:
            bin_idx = i
            break
    
    # 更新视频级别统计
    video_stats = stats['video_stats'][video_name]
    video_stats['frames'].append(frame_no)
    video_stats['voxel_counts'].append(voxel_count)
    video_stats['bin_counts'][bin_idx] += 1
    video_stats['total_frames'] += 1
    
    # 更新全局统计
    stats['global_stats']['total_frames'] += 1
    stats['global_stats']['bin_counts'][bin_idx] += 1

def finalize_statistics(stats):
    """完成统计计算，计算百分比"""
    # 计算全局百分比
    total_global = stats['global_stats']['total_frames']
    if total_global > 0:
        for i in range(5):
            stats['global_stats']['bin_percentages'][i] = (
                stats['global_stats']['bin_counts'][i] / total_global * 100
            )
    
    # 计算每个视频的百分比
    for video_name, video_stats in stats['video_stats'].items():
        total_video = video_stats['total_frames']
        video_stats['bin_percentages'] = []
        if total_video > 0:
            for i in range(5):
                percentage = video_stats['bin_counts'][i] / total_video * 100
                video_stats['bin_percentages'].append(percentage)
        else:
            video_stats['bin_percentages'] = [0.0] * 5
        
        # 添加统计摘要
        video_stats['summary'] = {
            'mean_voxels': np.mean(video_stats['voxel_counts']) if video_stats['voxel_counts'] else 0,
            'std_voxels': np.std(video_stats['voxel_counts']) if video_stats['voxel_counts'] else 0,
            'min_voxels': min(video_stats['voxel_counts']) if video_stats['voxel_counts'] else 0,
            'max_voxels': max(video_stats['voxel_counts']) if video_stats['voxel_counts'] else 0,
            'median_voxels': np.median(video_stats['voxel_counts']) if video_stats['voxel_counts'] else 0
        }

def save_statistics(stats, save_path):
    """保存统计信息到JSON和CSV文件"""
    # 保存详细的JSON统计
    stats_file = os.path.join(save_path, 'voxel_statistics.json')
    with open(stats_file, 'w', encoding='utf-8') as f:
        json.dump(stats, f, indent=2, ensure_ascii=False)
    
    # 生成简化的CSV报告
    csv_file = os.path.join(save_path, 'voxel_statistics_summary.csv')
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        import csv
        writer = csv.writer(f)
        writer.writerow(['Video', 'Total_Frames', '<1024', '1024-2048', '2048-4096', 
                        '4096-8192', '>8192', 'Mean_Voxels', 'Std_Voxels'])
        
        for video_name, video_stats in stats['video_stats'].items():
            row = [video_name, video_stats['total_frames']]
            row.extend([f"{count}({pct:.1f}%)" for count, pct in 
                       zip(video_stats['bin_counts'], video_stats['bin_percentages'])])
            row.extend([f"{video_stats['summary']['mean_voxels']:.1f}",
                       f"{video_stats['summary']['std_voxels']:.1f}"])
            writer.writerow(row)

if __name__ == '__main__':
    os.environ['CUDA_VISIBLE_DEVICES'] = "0"
    save_voxel = 6000
    use_mode = 'frame_exposure_time'
    device = torch.device("cuda:0")

    # 是否保留事件时序
    preserve_time_order = False
    data_path = r"/home/<USER>/STU/workspaces/ruihui/ViPT/data/COESOT/train"
    save_path = r"/home/<USER>/STU/workspaces/ruihui/ViPT/data/COESOT/train"
    video_files = os.listdir(data_path)
    dvs_img_interval = 1    
    # 创建体素生成器池
    voxel_generators = create_voxel_generator_pool(device)
    
    # 初始化统计
    voxel_stats = create_voxel_statistics() 

    for videoID in range(len(video_files)):
        foldName = video_files[videoID]
        # 检查是否为目录，如果是文件则跳过
        if not os.path.isdir(os.path.join(data_path, foldName)):
            print(f"==>> 跳过文件: {foldName}")
            continue
        print("==>> foldName: ", foldName)

        aedat4_file = foldName + '.aedat4'
        read_path = os.path.join(data_path, foldName, aedat4_file)
        if not os.path.exists(read_path):
            print(f"==>> 文件不存在，跳过: {read_path}")
            continue

        # read aeda4;
        frame_all = []
        frame_exposure_time = []
        frame_interval_time = []
        try:
            with AedatFile(read_path) as f:
                for frame in f['frames']:
                    frame_all.append(frame.image)
                    frame_exposure_time.append([frame.timestamp_start_of_exposure,
                                                frame.timestamp_end_of_exposure])  ## [1607928583397102, 1607928583401102]
                    frame_interval_time.append([frame.timestamp_start_of_frame,
                                                frame.timestamp_end_of_frame])

                # 读取事件数据
                events = np.hstack([packet for packet in f['events'].numpy()])
        except Exception as e:
            print(f"==>> 读取文件失败，跳过文件: {read_path}")
            print(f"==>> 错误信息: {str(e)}")
            continue
        if use_mode == 'frame_exposure_time':
            frame_timestamp = frame_exposure_time
        elif use_mode == 'frame_interval_time':
            frame_timestamp = frame_interval_time
        frame_num = len(frame_timestamp)

        save_dir = os.path.join(save_path, foldName, foldName+'_voxel_npy')
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        else:
            continue

        # GPU并行化优化：预先处理所有事件数据并转移到GPU，避免循环中重复传输
        print("预处理事件数据到GPU...")
        t_all = torch.tensor(events['timestamp']).unsqueeze(1).to(device)
        x_all = torch.tensor(events['x']).unsqueeze(1).to(device)
        y_all = torch.tensor(events['y']).unsqueeze(1).to(device)
        p_all = torch.tensor(events['polarity']).unsqueeze(1).to(device)
        
        # GPU并行化优化：预先计算所有帧的时间边界，避免循环中重复的numpy.where操作
        timestamps_np = events['timestamp']
        frame_indices = []
        print("预计算帧索引...")
        for frame_no in range(0, int(frame_num / dvs_img_interval) - 1):
            try:
                start_time = frame_timestamp[frame_no][0]
                end_time = frame_timestamp[frame_no][1]
                # 使用searchsorted优化索引查找，比numpy.where更快
                start_idx = np.searchsorted(timestamps_np, start_time, side='left')
                end_idx = np.searchsorted(timestamps_np, end_time, side='left')
                if start_idx < len(timestamps_np) and end_idx <= len(timestamps_np):
                    frame_indices.append((frame_no, start_idx, end_idx))
            except (IndexError, ValueError):
                continue
        
        # GPU并行化优化：增大批处理大小，提高GPU利用率
        batch_size = min(64, len(frame_indices))  # 优化批处理大小
        
        # 处理帧
        with tqdm(total=len(frame_indices), position=0, leave=True, desc="Processing") as pbar:
            # 批处理帧
            for batch_idx in range(0, len(frame_indices), batch_size):
                batch_frames = frame_indices[batch_idx:batch_idx+batch_size]
                
                for frame_no, start_idx, end_idx in batch_frames:
                    # GPU并行化优化：直接使用预计算的索引，避免重复的numpy.where操作
                    sub_event = events[start_idx:end_idx]
                    idx_length = end_idx - start_idx

                    # GPU并行化优化：直接使用预先传输到GPU的数据切片
                    t = t_all[start_idx:end_idx]
                    if start_idx == end_idx or idx_length < 10:
                        save_file_path = os.path.join(save_dir, 'frame{:0>4d}.npz'.format(frame_no))
                        np.savez_compressed(save_file_path, coor=np.zeros([100, 3]),
                                            features=np.zeros([100, 16]))
                        # 统计空帧的体素数量为100（填充值）
                        update_voxel_statistics(voxel_stats, foldName, frame_no, 100)
                        print('empty event frame ', frame_no)
                        pbar.update(1)
                        continue
                    else:
                        time_length = t[-1] - t[0]
                        # rescale the timestampes to start from 0 up to 1000
                        if time_length > 0:  # GPU并行化优化：避免除零错误
                            t = ((t-t[0]).float() / time_length) * 1000
                        else:
                            t = torch.zeros_like(t)
                    
                    # GPU并行化优化：直接使用预先传输到GPU的数据切片
                    all_idx = np.where(sub_event['polarity'] != -1)
                    neg_idx_cpu = np.where(sub_event['polarity'] == 0)  # 保持原有逻辑
                    
                    t = t[all_idx]
                    x = x_all[all_idx]
                    y = y_all[all_idx]
                    p = p_all[all_idx]
                    
                    # 保持原有的极性转换逻辑
                    if len(neg_idx_cpu) > 0:
                        p[neg_idx_cpu] = -1     # negtive voxel change from 0 to -1. because after append 0 operation.
                    current_events = torch.cat((t, x, y, p), dim=1)
                    data_dict = {'points': current_events}

                    # 快速场景分类（避免复杂计算）
                    scene_type = classify_scene_type(sub_event)
                    
                    # 选择对应的体素生成器（无需重新创建）
                    voxel_generator = voxel_generators[scene_type]
                    adaptive_save_voxel = get_adaptive_save_voxel(scene_type, save_voxel)

                    coor, features = transform_points_to_voxels_enhanced(
                        data_dict=data_dict,
                        voxel_generator=voxel_generator,
                        device=device,
                        preserve_order=preserve_time_order,
                        save_voxel=adaptive_save_voxel
                    )
                    
                    # 统计体素数量
                    actual_voxel_count = coor.shape[0]
                    update_voxel_statistics(voxel_stats, foldName, frame_no, actual_voxel_count)
                    
                    coor = coor.cpu().numpy()
                    features = features.cpu().numpy()
                    save_file_path = os.path.join(save_dir, 'frame{:0>4d}.npz'.format(frame_no))
                    np.savez_compressed(save_file_path, coor=coor, features=features)
                    # 更新进度条
                    pbar.update(1)
                
                # GPU并行化优化：定期清理GPU缓存，避免内存溢出
                if batch_idx % (batch_size * 4) == 0:
                    torch.cuda.empty_cache()
        print("==>> Done... ")
    
    # 完成统计并保存
    finalize_statistics(voxel_stats)
    save_statistics(voxel_stats, save_path)
    print("==>> 体素统计信息已保存到:", save_path)