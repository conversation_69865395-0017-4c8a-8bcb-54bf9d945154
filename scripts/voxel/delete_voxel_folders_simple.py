#!/usr/bin/env python3
"""
简化版本：删除 data/COESOT/train 路径下所有子文件夹中的 voxel2、voxel3、voxel4 文件夹
"""

import os
import shutil
from pathlib import Path


def main():
    # 设置基础路径
    base_path = Path("/home/<USER>/STU/workspaces/ruihui/ViPT/data/FELT/test")
    target_suffixes = ["voxel"]
    
    print(f"🔍 开始扫描路径: {base_path.absolute()}")
    
    if not base_path.exists():
        print(f"❌ 错误: 路径 {base_path} 不存在!")
        return
    
    deleted_count = 0
    
    # 遍历所有子文件夹
    for subfolder in base_path.iterdir():
        if not subfolder.is_dir():
            continue
            
        print(f"\n📂 检查: {subfolder.name}")
        
        # 在每个子文件夹中查找以指定后缀结尾的文件夹
        found_any = False
        for item in subfolder.iterdir():
            if not item.is_dir():
                continue
                
            # 检查文件夹名是否以目标后缀结尾
            for suffix in target_suffixes:
                if item.name.endswith(suffix):
                    found_any = True
                    try:
                        shutil.rmtree(item)
                        print(f"  ✅ 已删除: {item.name}")
                        deleted_count += 1
                    except Exception as e:
                        print(f"  ❌ 删除失败: {item.name} - {e}")
                    break  # 找到匹配的后缀就跳出内层循环
        
        if not found_any:
            print(f"  ➖ 未找到以voxel2/voxel3/voxel4结尾的文件夹")
    
    print(f"\n🎉 完成! 总共删除了 {deleted_count} 个文件夹")


if __name__ == "__main__":
    main()

