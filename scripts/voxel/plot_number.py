import pandas as pd
import matplotlib.pyplot as plt
import os

def plot_voxel_summary(csv_path):
    """
    读取CSV文件，计算并打印全局统计数据，然后生成柱状图。

    参数:
        csv_path (str): 指向 voxel_statistics_summary.csv 文件的路径。
    """
    # 检查文件是否存在
    if not os.path.exists(csv_path):
        print(f"错误：文件未找到于 '{csv_path}'")
        return

    # 读取CSV文件
    df = pd.read_csv(csv_path)

    # 定义体素范围的列名
    bin_columns = ['<1024', '1024-2048', '2048-4096', '4096-8192', '>8192']
    
    # 初始化一个字典来存储每个范围的总帧数
    total_bin_counts = {col: 0 for col in bin_columns}

    # 提取每个范围的帧数并求和
    # 格式为 "count(percentage%)"，我们只需要前面的count
    for col in bin_columns:
        # .str.split('(') 将字符串按'('分割成列表，.str[0] 取第一部分，然后转为整数求和
        total_bin_counts[col] = df[col].str.split('(', expand=True)[0].astype(int).sum()

    # 打印总计结果
    total_frames = df['Total_Frames'].sum()
    print("--- 所有视频序列的全局体素统计 ---")
    print(f"总计处理帧数: {total_frames}")
    for bin_name, count in total_bin_counts.items():
        percentage = (count / total_frames) * 100 if total_frames > 0 else 0
        print(f"范围 {bin_name}: {count} 帧 ({percentage:.1f}%)")

    # --- 绘制柱状图 ---
    bin_names = list(total_bin_counts.keys())
    counts = list(total_bin_counts.values())

    # 设置中文字体以正确显示标签
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 'SimHei' 是一个常用的中文字体
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

    fig, ax = plt.subplots(figsize=(10, 6))
    bars = ax.bar(bin_names, counts, color='skyblue')

    # 在柱状图上添加数值标签
    for bar in bars:
        yval = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2.0, yval, int(yval), va='bottom', ha='center')

    ax.set_xlabel('体素数量范围')
    ax.set_ylabel('总帧数')
    ax.set_title('所有视频的体素数量分布统计')
    plt.xticks(rotation=15) # 轻微旋转x轴标签以防重叠
    plt.tight_layout()
    
    # 保存图表到文件
    output_image_path = os.path.join(os.path.dirname(csv_path), 'voxel_distribution_summary.png')
    plt.savefig(output_image_path)
    print(f"\n柱状图已保存到: {output_image_path}")

    # 显示图表
    # plt.show()


if __name__ == '__main__':
    # 请将此路径替换为您的CSV文件的实际路径
    csv_file_path = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/FELT/train/voxel_statistics_summary.csv'
    plot_voxel_summary(csv_file_path)