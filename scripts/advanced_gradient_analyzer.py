#!/usr/bin/env python3
"""
高级梯度信息分析工具
提供更多分析功能，包括统计分析、异常检测、趋势分析等
"""

import re
import argparse
import matplotlib.pyplot as plt
import numpy as np
from collections import defaultdict
import pandas as pd
from scipy import stats
import seaborn as sns

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class AdvancedGradientAnalyzer:
    def __init__(self, log_file):
        self.log_file = log_file
        self.epochs = []
        self.gradient_data = defaultdict(lambda: defaultdict(list))
        
        # 梯度统计信息的字段映射
        self.field_mapping = {
            0: 'L2_norm',
            1: 'abs_mean', 
            2: 'abs_max',
            3: 'abs_min',
            4: 'mean',
            5: 'std',
            6: 'nan_count',
            7: 'zero_count'
        }
        
    def parse_log(self):
        """解析日志文件"""
        print("Parsing log file...")
        
        current_epoch = None
        epoch_pattern = r'\[train: (\d+), \d+ / \d+\]'
        grad_pattern = r'\[Grad Stats of (module\.backbone\.voxel_injectors\.\d+\..*?)\] L2 norm: ([\d\.e\-\+]+) \| abs_mean: ([\d\.e\-\+]+) \| abs_max: ([\d\.e\-\+]+) \| abs_min: ([\d\.e\-\+]+) \| mean: ([\d\.e\-\+\-]+) \| std: ([\d\.e\-\+]+) \| nan: (\d+) \| zero: (\d+)/\d+'
        
        with open(self.log_file, 'r', encoding='utf-8') as f:
            for line in f:
                # 检查是否是新的epoch
                epoch_match = re.search(epoch_pattern, line)
                if epoch_match:
                    current_epoch = int(epoch_match.group(1))
                    if current_epoch not in self.epochs:
                        self.epochs.append(current_epoch)
                
                # 检查是否是梯度统计信息
                grad_match = re.search(grad_pattern, line)
                if grad_match and current_epoch is not None:
                    module_name = grad_match.group(1)
                    
                    # 提取层名称
                    layer_match = re.match(r'module\.backbone\.voxel_injectors\.(\d+)\.(.*)', module_name)
                    if layer_match:
                        layer_idx = layer_match.group(1)
                        layer_name = f"voxel_injectors.{layer_idx}"
                        
                        # 解析梯度统计值
                        values = [float(grad_match.group(i)) for i in range(2, 8)]
                        values.extend([int(grad_match.group(8)), int(grad_match.group(9))])
                        
                        # 存储数据
                        for i, value in enumerate(values):
                            field_name = self.field_mapping[i]
                            self.gradient_data[layer_name][field_name].append((current_epoch, value))
        
        print(f"Parsing completed! Found {len(self.epochs)} epochs, {len(self.gradient_data)} layers")
    
    def get_layer_statistics(self, layer_name, field_name):
        """获取特定层和字段的统计信息"""
        if layer_name not in self.gradient_data or field_name not in self.gradient_data[layer_name]:
            return None
        
        data = self.gradient_data[layer_name][field_name]
        epochs, values = zip(*data)
        values = np.array(values)
        
        stats_dict = {
            'mean': np.mean(values),
            'std': np.std(values),
            'min': np.min(values),
            'max': np.max(values),
            'median': np.median(values),
            'q25': np.percentile(values, 25),
            'q75': np.percentile(values, 75),
            'trend_slope': self._calculate_trend(epochs, values),
            'stability': np.std(values) / np.mean(values) if np.mean(values) != 0 else float('inf')
        }
        
        return stats_dict
    
    def _calculate_trend(self, epochs, values):
        """计算趋势斜率"""
        if len(epochs) < 2:
            return 0
        slope, _, _, _, _ = stats.linregress(epochs, values)
        return slope
    
    def detect_anomalies(self, layer_name, field_name, threshold=3):
        """检测异常值（使用Z-score方法）"""
        if layer_name not in self.gradient_data or field_name not in self.gradient_data[layer_name]:
            return []
        
        data = self.gradient_data[layer_name][field_name]
        epochs, values = zip(*data)
        values = np.array(values)
        
        z_scores = np.abs(stats.zscore(values))
        anomaly_indices = np.where(z_scores > threshold)[0]
        
        anomalies = []
        for idx in anomaly_indices:
            anomalies.append({
                'epoch': epochs[idx],
                'value': values[idx],
                'z_score': z_scores[idx]
            })
        
        return anomalies
    
    def plot_comprehensive_analysis(self, selected_layers=None, save_path=None):
        """绘制综合分析图表"""
        if selected_layers is None:
            selected_layers = sorted(self.gradient_data.keys())[:6]  # 默认选择前6个层
        
        # 创建大图表
        fig = plt.figure(figsize=(20, 15))
        
        # 1. L2范数趋势图
        ax1 = plt.subplot(3, 3, 1)
        self._plot_field_trends(ax1, 'L2_norm', selected_layers, 'L2 Norm Trends')
        
        # 2. 绝对值平均趋势图
        ax2 = plt.subplot(3, 3, 2)
        self._plot_field_trends(ax2, 'abs_mean', selected_layers, 'Absolute Mean Trends')
        
        # 3. 标准差趋势图
        ax3 = plt.subplot(3, 3, 3)
        self._plot_field_trends(ax3, 'std', selected_layers, 'Standard Deviation Trends')
        
        # 4. 零值统计
        ax4 = plt.subplot(3, 3, 4)
        self._plot_field_trends(ax4, 'zero_count', selected_layers, 'Zero Count Statistics', use_log=False)
        
        # 5. 热力图 - 显示各层L2范数的变化
        ax5 = plt.subplot(3, 3, 5)
        self._plot_heatmap(ax5, 'L2_norm', selected_layers, 'L2 Norm Heatmap')
        
        # 6. 箱线图 - 显示各层L2范数的分布
        ax6 = plt.subplot(3, 3, 6)
        self._plot_boxplot(ax6, 'L2_norm', selected_layers, 'L2 Norm Distribution')
        
        # 7. 趋势斜率对比
        ax7 = plt.subplot(3, 3, 7)
        self._plot_trend_slopes(ax7, selected_layers, 'Trend Slopes Comparison')
        
        # 8. 稳定性分析
        ax8 = plt.subplot(3, 3, 8)
        self._plot_stability_analysis(ax8, selected_layers, 'Stability Analysis')
        
        # 9. 异常值检测
        ax9 = plt.subplot(3, 3, 9)
        self._plot_anomaly_detection(ax9, 'L2_norm', selected_layers, 'Anomaly Detection (L2 Norm)')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Comprehensive analysis saved to: {save_path}")
        
        plt.show()
    
    def _plot_field_trends(self, ax, field, layers, title, use_log=True):
        """绘制字段趋势图"""
        colors = plt.cm.tab10(np.linspace(0, 1, len(layers)))
        
        for i, layer in enumerate(layers):
            if layer in self.gradient_data and field in self.gradient_data[layer]:
                data = self.gradient_data[layer][field]
                if data:
                    epochs, values = zip(*data)
                    ax.plot(epochs, values, 'o-', label=layer, color=colors[i], 
                           linewidth=1.5, markersize=3, alpha=0.8)
        
        ax.set_xlabel('Epoch')
        ax.set_ylabel(field)
        ax.set_title(title)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=8)
        
        if use_log and field in ['L2_norm', 'abs_mean', 'abs_max', 'abs_min', 'std']:
            ax.set_yscale('log')
    
    def _plot_heatmap(self, ax, field, layers, title):
        """绘制热力图"""
        # 准备数据矩阵
        data_dict = {}
        all_epochs = set()

        for layer in layers:
            if layer in self.gradient_data and field in self.gradient_data[layer]:
                data = self.gradient_data[layer][field]
                if data:
                    epochs, values = zip(*data)
                    data_dict[layer] = dict(zip(epochs, values))
                    all_epochs.update(epochs)

        if data_dict:
            # 创建对齐的数据矩阵
            sorted_epochs = sorted(all_epochs)
            data_matrix = []
            layer_labels = []

            for layer in layers:
                if layer in data_dict:
                    row = [data_dict[layer].get(epoch, np.nan) for epoch in sorted_epochs]
                    data_matrix.append(row)
                    layer_labels.append(layer)

            if data_matrix:
                data_matrix = np.array(data_matrix)
                # 处理NaN值
                data_matrix = np.ma.masked_invalid(data_matrix)

                im = ax.imshow(data_matrix, aspect='auto', cmap='viridis', interpolation='nearest')
                ax.set_xlabel('Training Steps')
                ax.set_ylabel('Layers')
                ax.set_title(title)
                ax.set_yticks(range(len(layer_labels)))
                ax.set_yticklabels(layer_labels, fontsize=8)

                # 设置x轴标签（显示部分epoch）
                step = max(1, len(sorted_epochs) // 10)
                ax.set_xticks(range(0, len(sorted_epochs), step))
                ax.set_xticklabels([str(sorted_epochs[i]) for i in range(0, len(sorted_epochs), step)], fontsize=8)

                plt.colorbar(im, ax=ax)
    
    def _plot_boxplot(self, ax, field, layers, title):
        """绘制箱线图"""
        data_list = []
        labels = []
        
        for layer in layers:
            if layer in self.gradient_data and field in self.gradient_data[layer]:
                data = self.gradient_data[layer][field]
                if data:
                    _, values = zip(*data)
                    data_list.append(values)
                    labels.append(layer.split('.')[-1])  # 只显示层编号
        
        if data_list:
            ax.boxplot(data_list, labels=labels)
            ax.set_xlabel('Layer')
            ax.set_ylabel(field)
            ax.set_title(title)
            ax.tick_params(axis='x', rotation=45)
            if field in ['L2_norm', 'abs_mean', 'abs_max', 'abs_min', 'std']:
                ax.set_yscale('log')
    
    def _plot_trend_slopes(self, ax, layers, title):
        """绘制趋势斜率对比"""
        fields = ['L2_norm', 'abs_mean', 'std']
        slopes_data = {field: [] for field in fields}
        layer_names = []
        
        for layer in layers:
            layer_names.append(layer.split('.')[-1])
            for field in fields:
                stats_dict = self.get_layer_statistics(layer, field)
                if stats_dict:
                    slopes_data[field].append(stats_dict['trend_slope'])
                else:
                    slopes_data[field].append(0)
        
        x = np.arange(len(layer_names))
        width = 0.25
        
        for i, field in enumerate(fields):
            ax.bar(x + i * width, slopes_data[field], width, label=field, alpha=0.8)
        
        ax.set_xlabel('Layer')
        ax.set_ylabel('Trend Slope')
        ax.set_title(title)
        ax.set_xticks(x + width)
        ax.set_xticklabels(layer_names)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_stability_analysis(self, ax, layers, title):
        """绘制稳定性分析"""
        fields = ['L2_norm', 'abs_mean', 'std']
        stability_data = {field: [] for field in fields}
        layer_names = []
        
        for layer in layers:
            layer_names.append(layer.split('.')[-1])
            for field in fields:
                stats_dict = self.get_layer_statistics(layer, field)
                if stats_dict and stats_dict['stability'] != float('inf'):
                    stability_data[field].append(stats_dict['stability'])
                else:
                    stability_data[field].append(0)
        
        x = np.arange(len(layer_names))
        width = 0.25
        
        for i, field in enumerate(fields):
            ax.bar(x + i * width, stability_data[field], width, label=field, alpha=0.8)
        
        ax.set_xlabel('Layer')
        ax.set_ylabel('Coefficient of Variation')
        ax.set_title(title)
        ax.set_xticks(x + width)
        ax.set_xticklabels(layer_names)
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    def _plot_anomaly_detection(self, ax, field, layers, title):
        """绘制异常值检测结果"""
        for i, layer in enumerate(layers):
            anomalies = self.detect_anomalies(layer, field)
            if anomalies:
                epochs = [a['epoch'] for a in anomalies]
                values = [a['value'] for a in anomalies]
                ax.scatter(epochs, values, label=f'{layer} anomalies', alpha=0.7, s=50)
        
        ax.set_xlabel('Epoch')
        ax.set_ylabel(field)
        ax.set_title(title)
        ax.legend(fontsize=8)
        ax.grid(True, alpha=0.3)
        if field in ['L2_norm', 'abs_mean', 'abs_max', 'abs_min', 'std']:
            ax.set_yscale('log')

def main():
    parser = argparse.ArgumentParser(description='Advanced Gradient Analysis Tool')
    parser.add_argument('log_file', help='Log file path')
    parser.add_argument('--layers', type=str, help='Layer indices to analyze (comma-separated)')
    parser.add_argument('--save', type=str, help='Save comprehensive analysis chart')
    parser.add_argument('--stats', action='store_true', help='Show detailed statistics')
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = AdvancedGradientAnalyzer(args.log_file)
    analyzer.parse_log()
    
    # 选择层
    available_layers = sorted(analyzer.gradient_data.keys())
    if args.layers:
        layer_indices = [int(x.strip()) for x in args.layers.split(',')]
        selected_layers = [available_layers[i] for i in layer_indices if i < len(available_layers)]
    else:
        selected_layers = available_layers[:6]  # 默认选择前6个层
    
    print(f"Selected layers: {selected_layers}")
    
    # 绘制综合分析图表
    analyzer.plot_comprehensive_analysis(selected_layers, args.save)
    
    # 显示详细统计信息
    if args.stats:
        print("\n=== Detailed Statistics ===")
        for layer in selected_layers:
            print(f"\nLayer: {layer}")
            for field in ['L2_norm', 'abs_mean', 'std']:
                stats_dict = analyzer.get_layer_statistics(layer, field)
                if stats_dict:
                    print(f"  {field}:")
                    print(f"    Mean: {stats_dict['mean']:.6e}")
                    print(f"    Std: {stats_dict['std']:.6e}")
                    print(f"    Trend slope: {stats_dict['trend_slope']:.6e}")
                    print(f"    Stability (CV): {stats_dict['stability']:.6f}")

if __name__ == '__main__':
    main()
