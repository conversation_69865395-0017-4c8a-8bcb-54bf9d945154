import re
import matplotlib.pyplot as plt
from collections import defaultdict
import argparse # 导入 argparse
import os       # 导入 os

def parse_log_for_training_losses(log_filepath, val_field="val"):
    """
    解析日志文件以提取每个 epoch 的平均验证损失和IoU。

    Args:
        log_filepath: 日志文件路径
        val_field: 用于匹配的字段名，默认为"val"
    """
    epoch_data = defaultdict(lambda: defaultdict(list))
    train_line_re = re.compile(rf"\[{val_field}: (\d+),.*")
    loss_re = re.compile(r"Loss/(\w+): ([\d.]+)")
    iou_re = re.compile(r"IoU: ([\d.]+)")
    current_epoch = -1

    try:
        with open(log_filepath, 'r', encoding='utf-8') as f:
            for line in f:
                train_match = train_line_re.match(line)
                if train_match:
                    epoch_num = int(train_match.group(1))
                    current_epoch = epoch_num
                    # 提取损失值
                    losses_found = loss_re.findall(line)
                    for loss_name, loss_value in losses_found:
                        if loss_name in ['total', 'giou', 'l1', 'location']:
                            epoch_data[current_epoch][f"Loss/{loss_name}"].append(float(loss_value))

                    # 提取IoU值
                    iou_match = iou_re.search(line)
                    if iou_match:
                        iou_value = float(iou_match.group(1))
                        epoch_data[current_epoch]["IoU"].append(iou_value)
    except FileNotFoundError:
        print(f"Error: Log file '{log_filepath}' not found.")
        return []
    except Exception as e:
        print(f"An error occurred while reading or parsing the log file: {e}")
        return []

    averaged_epoch_losses = []
    for epoch_num in sorted(epoch_data.keys()):
        avg_losses = {'epoch': epoch_num}
        for loss_type, values in epoch_data[epoch_num].items():
            if values:
                avg_losses[loss_type] = sum(values) / len(values)
            else:
                avg_losses[loss_type] = 0.0
        averaged_epoch_losses.append(avg_losses)
    return averaged_epoch_losses

def plot_losses(averaged_epoch_losses, output_base_filename="training_losses", output_dir="logs/loss"):
    """
    绘制每个 epoch 的平均损失和IoU，并在节点上标记数值。
    图片将保存到指定的 output_dir 中，文件名为 output_base_filename.png。
    """
    if not averaged_epoch_losses:
        print("No data to plot.")
        return

    epochs = [data['epoch'] for data in averaged_epoch_losses]
    loss_types_to_plot = ['Loss/total', 'Loss/giou', 'Loss/l1', 'Loss/location']

    # 创建包含两个子图的图形：一个用于损失，一个用于IoU
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))

    # 绘制损失图（第一个子图）
    for loss_type in loss_types_to_plot:
        values = [data.get(loss_type) for data in averaged_epoch_losses]
        plot_epochs = []
        plot_values = []
        for i, v in enumerate(values):
            if v is not None:
                plot_epochs.append(epochs[i])
                plot_values.append(v)

        if plot_values:
            ax1.plot(plot_epochs, plot_values, marker='o', linestyle='-', label=loss_type)
            for i in range(len(plot_epochs)):
                epoch_val = plot_epochs[i]
                loss_val = plot_values[i]
                ax1.text(epoch_val, loss_val, f"{loss_val:.4f}", fontsize=6, ha='center', va='bottom')

    ax1.set_xlabel("Epoch")
    ax1.set_ylabel("Average Loss Value")
    ax1.set_title("Average Training Loss per Epoch with Value Labels")
    ax1.legend(loc='best')
    ax1.grid(True)

    # 设置损失图的x轴刻度
    if epochs:
        if len(set(epochs)) <= 20:
             ax1.set_xticks(sorted(list(set(epochs))))
        else:
            step = max(1, len(set(epochs)) // 15)
            ax1.set_xticks(range(min(epochs), max(epochs)+1, step))

    ax1.tick_params(axis='x', rotation=45)

    # 绘制IoU图（第二个子图）
    iou_values = [data.get('IoU') for data in averaged_epoch_losses]
    plot_epochs_iou = []
    plot_values_iou = []
    for i, v in enumerate(iou_values):
        if v is not None:
            plot_epochs_iou.append(epochs[i])
            plot_values_iou.append(v)

    if plot_values_iou:
        ax2.plot(plot_epochs_iou, plot_values_iou, marker='s', linestyle='-', label='IoU', color='green')
        for i in range(len(plot_epochs_iou)):
            epoch_val = plot_epochs_iou[i]
            iou_val = plot_values_iou[i]
            ax2.text(epoch_val, iou_val, f"{iou_val:.4f}", fontsize=6, ha='center', va='bottom')

    ax2.set_xlabel("Epoch")
    ax2.set_ylabel("IoU Value")
    ax2.set_title("IoU per Epoch with Value Labels")
    ax2.legend(loc='best')
    ax2.grid(True)

    # 设置IoU图的x轴刻度
    if epochs:
        if len(set(epochs)) <= 20:
             ax2.set_xticks(sorted(list(set(epochs))))
        else:
            step = max(1, len(set(epochs)) // 15)
            ax2.set_xticks(range(min(epochs), max(epochs)+1, step))

    ax2.tick_params(axis='x', rotation=45)
    plt.tight_layout()

    # 创建输出目录（如果不存在）
    if not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir)
            print(f"Created directory: {output_dir}")
        except OSError as e:
            print(f"Error creating directory {output_dir}: {e}")
            # 如果目录创建失败，可以选择不保存或保存到当前目录
            # 这里我们选择继续尝试保存到指定路径，如果失败，savefig会报错
            pass
    
    output_filepath = os.path.join(output_dir, f"{output_base_filename}.png")

    try:
        plt.savefig(output_filepath, dpi=300, bbox_inches='tight')
        print(f"Plot saved as {output_filepath}")
    except Exception as e:
        print(f"Failed to save plot to {output_filepath}: {e}")

    # plt.show()

if __name__ == "__main__":
    # 设置命令行参数解析器
    parser = argparse.ArgumentParser(description="Parse training log and plot losses and IoU.")
    parser.add_argument("log_file", help="Path to the training log file (e.g., adapter.log)")
    parser.add_argument("--output_name", default="val_losses_iou",
                        help="Base name for the output plot image (without .png extension). Default: val_losses_iou")
    parser.add_argument("--output_dir", default="logs/loss",
                        help="Directory to save the plot image. Default: logs/loss")
    parser.add_argument("--field", default="val",
                        help="Field name to match in log lines (e.g., 'val', 'train'). Default: val")

    args = parser.parse_args()

    log_file_path = args.log_file # 从命令行获取日志文件路径
    output_image_base_name = args.output_name # 从命令行获取图片基本名称
    output_directory = args.output_dir # 从命令行获取输出目录
    val_field = args.field # 从命令行获取val字段名

    parsed_data = parse_log_for_training_losses(log_file_path, val_field)

    if parsed_data:
        print(f"\nExtracted Average Losses and IoU per Epoch (using field '{val_field}'):")
        for epoch_summary in parsed_data:
            print(f"Epoch {epoch_summary['epoch']}:")
            for metric_name, avg_value in epoch_summary.items():
                if metric_name != 'epoch':
                    print(f"  {metric_name}: {avg_value:.5f}")

        plot_losses(parsed_data, output_base_filename=output_image_base_name, output_dir=output_directory)
    else:
        print(f"No training data found for field '{val_field}' or file could not be processed.")
