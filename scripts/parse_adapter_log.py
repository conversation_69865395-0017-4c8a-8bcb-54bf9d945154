#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练日志解析器 - 提取参数更新和loss信息
从adapter.log中提取每个epoch的参数更新信息和训练loss数据
"""

import re
import json
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Optional


class AdapterLogParser:
    def __init__(self, log_file: str):
        self.log_file = Path(log_file)
        self.param_pattern = re.compile(
            r'\[Param/Grad/Update Mean\] param: ([\d\.\-e]+), grad: ([\d\.\-e]+), update: ([\d\.\-e]+), lr\*update: ([\d\.\-e]+)'
        )
        self.count_pattern = re.compile(
            r'\[Param Count\] total: (\d+), trainable: (\d+), voxel: (\d+), with_grad: (\d+)'
        )
        self.train_pattern = re.compile(
            r'\[train: (\d+), (\d+) / (\d+)\].*?Loss/total: ([\d\.]+).*?Loss/giou: ([\d\.]+).*?Loss/l1: ([\d\.]+).*?Loss/location: ([\d\.]+).*?IoU: ([\d\.]+)'
        )
        
    def parse_log(self) -> Dict:
        """解析日志文件，提取每个epoch的数据"""
        if not self.log_file.exists():
            raise FileNotFoundError(f"日志文件不存在: {self.log_file}")

        epochs_data = {}
        current_epoch = None
        current_step = None
        step_mapping = {}  # 记录每个epoch的步数映射

        with open(self.log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # 检测训练信息行
            train_match = self.train_pattern.search(line)
            if train_match:
                epoch = int(train_match.group(1))
                current_step_num = int(train_match.group(2))
                total_steps = int(train_match.group(3))

                # 动态确定关键步数（通常是中间步数和最后步数）
                if epoch not in step_mapping:
                    # 对于234步的情况，选择200和234作为关键步数
                    if total_steps == 234:
                        step_mapping[epoch] = {
                            'mid_step': 200,
                            'final_step': 234
                        }
                    elif total_steps == 117:
                        step_mapping[epoch] = {
                            'mid_step': 50,
                            'final_step': 100
                        }
                    else:
                        # 通用情况：选择中间和最后步数
                        step_mapping[epoch] = {
                            'mid_step': total_steps // 2,
                            'final_step': total_steps
                        }

                # 只记录中间步数和最后步数的数据
                mid_step = step_mapping[epoch]['mid_step']
                final_step = step_mapping[epoch]['final_step']

                if current_step_num not in [mid_step, final_step]:
                    i += 1
                    continue

                # 初始化epoch数据结构
                if epoch not in epochs_data:
                    epochs_data[epoch] = {
                        f'step_{mid_step}': {'loss_info': None, 'param_updates': []},
                        f'step_{final_step}': {'loss_info': None, 'param_updates': []}
                    }

                # 保存loss信息
                loss_info = {
                    'total': float(train_match.group(4)),
                    'giou': float(train_match.group(5)),
                    'l1': float(train_match.group(6)),
                    'location': float(train_match.group(7)),
                    'iou': float(train_match.group(8))
                }

                step_key = f'step_{current_step_num}'
                epochs_data[epoch][step_key]['loss_info'] = loss_info
                current_epoch = epoch
                current_step = current_step_num
                
            # 检测参数更新信息
            elif self.param_pattern.search(line) and current_epoch is not None:
                param_match = self.param_pattern.search(line)
                if param_match:
                    # 查找下一行的参数计数信息
                    if i + 1 < len(lines):
                        next_line = lines[i + 1].strip()
                        count_match = self.count_pattern.search(next_line)
                        if count_match:
                            param_update = {
                                'param_mean': float(param_match.group(1)),
                                'grad_mean': float(param_match.group(2)),
                                'update': float(param_match.group(3)),
                                'lr_update': float(param_match.group(4)),
                                'total_params': int(count_match.group(1)),
                                'trainable_params': int(count_match.group(2)),
                                'voxel_params': int(count_match.group(3)),
                                'with_grad_params': int(count_match.group(4))
                            }
                            
                            # 只保存第一个（去重复）
                            step_key = f'step_{current_step}'
                            if not epochs_data[current_epoch][step_key]['param_updates']:
                                epochs_data[current_epoch][step_key]['param_updates'].append(param_update)
            
            i += 1
        
        return epochs_data
    
    def save_to_json(self, data: Dict, output_file: str):
        """保存数据到JSON文件"""
        output_path = Path(output_file)
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"数据已保存到: {output_path}")
    
    def save_to_csv(self, data: Dict, output_file: str):
        """保存数据到CSV文件"""
        import csv

        output_path = Path(output_file)
        with open(output_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # 写入表头
            header = [
                'epoch', 'step',
                'loss_total', 'loss_giou', 'loss_l1', 'loss_location', 'iou',
                'param_mean', 'grad_mean', 'update', 'lr_update',
                'total_params', 'trainable_params', 'voxel_params', 'with_grad_params'
            ]
            writer.writerow(header)

            # 写入数据
            for epoch in sorted(data.keys()):
                epoch_data = data[epoch]
                # 动态获取该epoch的所有步数
                for step_key in sorted(epoch_data.keys()):
                    step_data = epoch_data[step_key]
                    step_num = int(step_key.split('_')[1])  # 从step_xxx中提取数字

                    if step_data['loss_info'] and step_data['param_updates']:
                        loss = step_data['loss_info']
                        param = step_data['param_updates'][0]  # 只取第一个（去重复）

                        row = [
                            epoch, step_num,
                            loss['total'], loss['giou'], loss['l1'], loss['location'], loss['iou'],
                            param['param_mean'], param['grad_mean'], param['update'], param['lr_update'],
                            param['total_params'], param['trainable_params'],
                            param['voxel_params'], param['with_grad_params']
                        ]
                        writer.writerow(row)

        print(f"CSV数据已保存到: {output_path}")
    
    def print_summary(self, data: Dict):
        """打印数据摘要"""
        print(f"\n=== 解析结果摘要 ===")
        print(f"总共解析了 {len(data)} 个epoch的数据")

        # 统计每个epoch的数据完整性
        complete_epochs = 0
        all_steps = set()
        for epoch, epoch_data in data.items():
            epoch_complete = True
            for step_key, step_data in epoch_data.items():
                all_steps.add(step_key)
                if not (step_data['loss_info'] is not None and len(step_data['param_updates']) > 0):
                    epoch_complete = False
                    break
            if epoch_complete:
                complete_epochs += 1

        print(f"完整数据的epoch数量: {complete_epochs}")
        print(f"检测到的步数类型: {sorted(all_steps)}")

        # 显示前几个epoch的示例数据
        print(f"\n=== 前3个epoch的示例数据 ===")
        for epoch in sorted(list(data.keys())[:3]):
            print(f"\nEpoch {epoch}:")
            epoch_data = data[epoch]
            for step_key in sorted(epoch_data.keys()):
                step_data = epoch_data[step_key]
                step_num = int(step_key.split('_')[1])
                if step_data['loss_info'] and step_data['param_updates']:
                    loss = step_data['loss_info']
                    param = step_data['param_updates'][0]
                    print(f"  Step {step_num}: Loss={loss['total']:.5f}, IoU={loss['iou']:.5f}, "
                          f"Param={param['param_mean']:.6e}, Grad={param['grad_mean']:.6e}")


def main():
    parser = argparse.ArgumentParser(description='解析adapter.log训练日志文件')
    parser.add_argument('log_file', help='输入的日志文件路径')
    parser.add_argument('--output', '-o', default='adapter_parsed', 
                       help='输出文件前缀 (默认: adapter_parsed)')
    parser.add_argument('--format', '-f', choices=['json', 'csv', 'both'], 
                       default='both', help='输出格式 (默认: both)')
    
    args = parser.parse_args()
    
    try:
        # 解析日志
        parser_obj = AdapterLogParser(args.log_file)
        print(f"正在解析日志文件: {args.log_file}")
        data = parser_obj.parse_log()
        
        # 打印摘要
        parser_obj.print_summary(data)
        
        # 保存数据
        if args.format in ['json', 'both']:
            parser_obj.save_to_json(data, f"{args.output}.json")
        
        if args.format in ['csv', 'both']:
            parser_obj.save_to_csv(data, f"{args.output}.csv")
        
        print(f"\n解析完成！")
        
    except Exception as e:
        print(f"错误: {e}")
        return 1
    
    return 0


if __name__ == '__main__':
    exit(main())
