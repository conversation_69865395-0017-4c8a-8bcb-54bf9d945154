import os

def rename_files_in_subfolders(train_folder):
    # 遍历train文件夹内的所有子文件夹
    for root, dirs, files in os.walk(train_folder):
        for dir_name in dirs:
            # 获取子文件夹的完整路径
            subfolder_path = os.path.join(root, dir_name)
            
            # 遍历子文件夹内的所有文件
            for filename in os.listdir(subfolder_path):
                # 检查文件是否以特定后缀结尾
                if filename.endswith('timestamp.txt'):
                # if filename.endswith('_events.txt') or filename.endswith('_timestamp.txt') or filename.endswith('_timestamp2.txt'):
                    # 将文件名按 '_' 分割
                    parts = filename.split('_')
                    # 构造新的文件名，仅保留子文件夹名和后缀
                    new_filename = f"{dir_name}_{parts[-1]}"
                    old_file_path = os.path.join(subfolder_path, filename)
                    new_file_path = os.path.join(subfolder_path, new_filename)
                            
                    # 重命名文件
                    try:
                        os.rename(old_file_path, new_file_path)
                        print(f"Renamed: {old_file_path} -> {new_file_path}")
                    except Exception as e:
                        print(f"Error renaming {old_file_path}: {e}")
                    break  
                            

if __name__ == "__main__":
    train_folder = "data/VisEvent/test"  # 替换为你的train文件夹路径
    rename_files_in_subfolders(train_folder)