import os

# 指定要遍历的文件夹路径
folder_path = '/home/<USER>/STU/workspaces/ruihui/ViPT/data/FELT/test'

# 存储结果的列表
list_of_subdirectories = []

# 遍历指定文件夹
for subdir, dirs, files in os.walk(folder_path):
    for dir in dirs:
        # 检查子文件夹名是否以'voxel'结尾
        if dir.endswith('npy'):
            parent_dir = os.path.basename(subdir)
            # 将上一级文件夹名称添加到列表中
            list_of_subdirectories.append(parent_dir)
        
            
            

# 将结果写入list.txt文件
with open('test.txt', 'w') as f:
    for item in list_of_subdirectories:
        f.write("%s\n" % item)
